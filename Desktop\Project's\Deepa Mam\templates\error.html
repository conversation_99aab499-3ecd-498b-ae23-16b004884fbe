<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error {{ error_code }} | Meeting Recorder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF8C00;  /* Dark Orange */
            --primary-dark: #E67E00;
            --bg-dark: #121212;
            --card-dark: #1E1E1E;
            --text-primary: #E0E0E0;
            --text-secondary: #B0B0B0;
            --border-color: #333333;
        }

        body {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
            background-color: var(--bg-dark);
            color: var(--text-primary);
        }

        .error-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .error-card {
            max-width: 600px;
            background-color: var(--card-dark);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            width: 100%;
        }

        .error-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .error-code {
            font-size: 100px;
            font-weight: bold;
            line-height: 1;
        }

        .btn-home {
            border-radius: 30px;
            padding: 10px 30px;
            margin-top: 20px;
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--bg-dark);
        }

        .debug-info {
            margin-top: 30px;
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .alert-light {
            background-color: #2E2E2E;
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        /* Error type specific colors */
        .text-warning {
            color: var(--primary-color) !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        /* Remove all hover effects */
        a, button {
            transition: none !important;
        }
        
        a:hover, button:hover {
            transform: none !important;
            box-shadow: none !important;
            background-color: inherit !important;
            color: inherit !important;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .error-code {
                font-size: 80px;
            }
            
            .error-card {
                padding: 20px !important;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="card error-card p-5">
            <div class="card-body text-center">
                {% if error_code == 404 %}
                    <div class="error-icon text-warning">
                        <i class="fas fa-map-signs"></i>
                    </div>
                    <div class="error-code text-warning">404</div>
                    <h2>Page Not Found</h2>
                    <p class="mb-4">The page you are looking for might have been removed or doesn't exist.</p>
                {% elif error_code == 500 %}
                    <div class="error-icon text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-code text-danger">500</div>
                    <h2>Server Error</h2>
                    <p class="mb-4">Something went wrong on our end. Please try again later.</p>
                {% else %}
                    <div class="error-icon text-primary">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="error-code text-primary">{{ error_code }}</div>
                    <h2>Error Occurred</h2>
                    <p class="mb-4">An unexpected error has occurred.</p>
                {% endif %}

                <div class="alert alert-light">
                    {{ message }}
                </div>

                <a href="/" class="btn btn-home">
                    <i class="fas fa-home me-2"></i> Return to Home
                </a>

                {% if config.DEBUG %}
                <div class="debug-info">
                    <strong>Debug Information:</strong>
                    <pre>{{ request.path }} - {{ error_code }}</pre>
                    <p>Check the application logs for more details.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>