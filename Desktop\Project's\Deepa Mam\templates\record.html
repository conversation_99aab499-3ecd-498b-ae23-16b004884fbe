{% extends "base.html" %}

{% block title %}Record Meeting{% endblock %}

{% block extra_css %}
<style>
    :root {
        --dark-bg: #121212;
        --dark-card: #1E1E1E;
        --dark-primary: #FF8C00;
        --dark-border: #333333;
        --dark-text: #E0E0E0;
        --dark-text-secondary: #AAAAAA;
        --dark-input-bg: #2D2D2D;
        --dark-highlight: rgba(255, 140, 0, 0.3);
    }
    
    body {
        background-color: var(--dark-bg);
        color: var(--dark-text);
    }
    
    .card {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
    }
    
    .card-header {
        background-color: rgba(0, 0, 0, 0.2);
        border-bottom-color: var(--dark-border);
    }
    
    /* Form elements */
    .form-control, .form-select {
        background-color: var(--dark-input-bg);
        border-color: var(--dark-border);
        color: var(--dark-text);
    }
    
    .form-control:focus, .form-select:focus {
        background-color: var(--dark-input-bg);
        border-color: var(--dark-primary);
        box-shadow: 0 0 0 0.25rem rgba(255, 140, 0, 0.25);
        color: var(--dark-text);
    }
    
    .form-text {
        color: var(--dark-text-secondary);
    }
    
    .form-select option {
        background-color: var(--dark-input-bg);
        color: var(--dark-text);
    }
    
    .form-label {
        color: var(--dark-text);
    }
    
    /* Buttons */
    .btn-primary {
        background-color: var(--dark-primary);
        border-color: var(--dark-primary);
        color: #000000;
    }
    
    .btn-outline-secondary {
        color: var(--dark-text);
        border-color: #555555;
    }
    
    /* Typography */
    h2, h3, h5 {
        color: var(--dark-text);
    }
    
    /* Lists */
    ul {
        color: var(--dark-text);
    }
    
    /* Icons */
    .text-success {
        color: #4ddb6e !important;
    }
    
    .text-danger {
        color: #ff6b6b !important;
    }
    
    .fas {
        color: inherit;
    }
    
    /* Alerts */
    .alert-info {
        background-color: rgba(23, 162, 184, 0.2);
        color: #8bdaed;
        border-color: rgba(23, 162, 184, 0.3);
    }
    
    /* Progress bar */
    .progress {
        background-color: var(--dark-input-bg);
    }
    
    /* Don't touch the actual progress bar colors as they communicate volume levels */
    .progress-bar.bg-success {
        background-color: #198754 !important;
    }
    
    .progress-bar.bg-warning {
        background-color: #ffc107 !important;
    }
    
    .progress-bar.bg-danger {
        background-color: #dc3545 !important;
    }
    
    /* Responsive styling */
    @media (max-width: 767px) {
        .btn-lg {
            padding: 0.5rem 1rem;
            font-size: 1.1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header">
                <h2 class="mb-0">Record a New Meeting</h2>
            </div>
            <div class="card-body">
                <form action="{{ url_for('record_meeting') }}" method="post">
                    <div class="mb-3">
                        <label for="title" class="form-label">Meeting Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                        <div class="form-text">Give your meeting a descriptive title</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Participants (Optional)</label>
                        <textarea class="form-control" name="participants" rows="2" placeholder="Enter names of meeting participants..."></textarea>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-microphone me-2"></i> Start Recording
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card shadow-sm mt-4">
            <div class="card-header">
                <h3 class="mb-0">Recording Tips</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-check text-success me-2"></i> Do's:</h5>
                        <ul>
                            <li>Ensure your microphone is correctly configured</li>
                            <li>Speak clearly for better transcription quality</li>
                            <li>Introduce speakers when possible</li>
                            <li>Summarize key points at the end of the meeting</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-times text-danger me-2"></i> Don'ts:</h5>
                        <ul>
                            <li>Record meetings without necessary permissions</li>
                            <li>Place microphone near sources of noise</li>
                            <li>Talk over each other (for better transcription)</li>
                            <li>Record confidential information unless secure</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="mb-0">Audio Settings</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Your system audio will be recorded.
                </div>
                
                <div class="mb-3">
                    <label for="audioSource" class="form-label">Audio Input Source</label>
                    <select class="form-select" id="audioSource">
                        <option value="" selected>Default Microphone</option>
                        <!-- Will be populated by JavaScript -->
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Input Level</label>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" id="volume-meter" style="width: 0%"></div>
                    </div>
                </div>
                
                <button type="button" class="btn btn-outline-secondary btn-sm" id="test-audio">
                    <i class="fas fa-volume-up me-1"></i> Test Microphone
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Get available audio devices
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
            navigator.mediaDevices.enumerateDevices()
                .then(function(devices) {
                    const audioSelect = document.getElementById('audioSource');
                    
                    devices.forEach(function(device) {
                        if (device.kind === 'audioinput') {
                            const option = document.createElement('option');
                            option.value = device.deviceId;
                            option.text = device.label || `Microphone ${audioSelect.length + 1}`;
                            audioSelect.appendChild(option);
                        }
                    });
                })
                .catch(function(err) {
                    console.error('Error enumerating devices: ', err);
                });
        }
        
        // Audio level meter
        let audioContext;
        let microphone;
        let analyser;
        let dataArray;
        let animationId;
        
        document.getElementById('test-audio').addEventListener('click', function() {
            if (audioContext) {
                stopAudioVisualization();
                this.innerHTML = '<i class="fas fa-volume-up me-1"></i> Test Microphone';
                return;
            }
            
            this.innerHTML = '<i class="fas fa-stop-circle me-1"></i> Stop Test';
            
            // Start audio visualization
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
            
            const bufferLength = analyser.frequencyBinCount;
            dataArray = new Uint8Array(bufferLength);
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function(stream) {
                    microphone = audioContext.createMediaStreamSource(stream);
                    microphone.connect(analyser);
                    
                    visualize();
                })
                .catch(function(err) {
                    console.error('Error accessing microphone: ', err);
                    alert('Unable to access microphone. Please check your settings.');
                    document.getElementById('test-audio').innerHTML = 
                        '<i class="fas fa-volume-up me-1"></i> Test Microphone';
                });
        });
        
        function visualize() {
            analyser.getByteFrequencyData(dataArray);
            
            // Calculate volume level
            let sum = 0;
            for (let i = 0; i < dataArray.length; i++) {
                sum += dataArray[i];
            }
            
            const average = sum / dataArray.length;
            const volumeLevel = Math.min(100, Math.round(average * 2));
            
            document.getElementById('volume-meter').style.width = volumeLevel + '%';
            
            // Update color based on level
            const volumeBar = document.getElementById('volume-meter');
            if (volumeLevel < 30) {
                volumeBar.className = 'progress-bar bg-success';
            } else if (volumeLevel < 70) {
                volumeBar.className = 'progress-bar bg-warning';
            } else {
                volumeBar.className = 'progress-bar bg-danger';
            }
            
            animationId = requestAnimationFrame(visualize);
        }
        
        function stopAudioVisualization() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            if (microphone) {
                microphone.disconnect();
            }
            
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            
            document.getElementById('volume-meter').style.width = '0%';
        }
    });
</script>
{% endblock %}