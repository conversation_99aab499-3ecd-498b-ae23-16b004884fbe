{% extends "base.html" %}

{% block title %}Meeting Recorder - Home{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #f98900;  /* Dark Orange */
        --primary-dark: #E67E00;
        --bg-dark: #121212;
        --card-dark: #1E1E1E;
        --text-primary: #E0E0E0;
        --text-secondary: #B0B0B0;
        --border-color: #333333;
        --success-color: #4CAF50;
    }
    
    body {
        background-color: var(--bg-dark);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--text-primary);
    }
    
    .card {
        background-color: var(--card-dark);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        background-color: rgba(255, 140, 0, 0.1);
        border-bottom: 1px solid var(--border-color);
        color: var(--primary-color);
        padding: 1rem 1.5rem;
    }
    
    .card-title {
        color: var(--primary-color);
        font-weight: 600;
    }
    
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: #121212;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
    }
    
    .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--text-primary);
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
    }
    
    .btn-outline-secondary:hover {
        background-color: transparent;
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .list-group-item {
        background-color: var(--card-dark);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .list-group-item-action {
        color: var(--text-primary);
    }
    
    .progress {
        background-color: #2E2E2E;
    }
    
    .progress-bar {
        background-color: var(--primary-color);
    }
    
    .lead {
        color: var(--text-secondary);
        font-weight: 400;
    }
    
    .text-muted {
        color: var(--text-secondary) !important;
    }
    
    .alert-info {
        background-color: rgba(255, 140, 0, 0.1);
        border-color: var(--border-color);
        color: var(--primary-color);
    }
    
    .form-control {
        background-color: #2E2E2E;
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus {
        background-color: #2E2E2E;
        border-color: var(--primary-color);
        color: var(--text-primary);
        box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
    }
    
    .form-label {
        color: var(--text-primary);
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .btn {
            width: 100%;
            margin-bottom: 0.75rem;
        }
        
        .d-md-flex {
            display: block !important;
        }
        
        .col-md-8, .col-md-4 {
            padding-left: 15px;
            padding-right: 15px;
        }
    }
    
    /* No hover effects */
    a, button, .list-group-item-action {
        transition: none !important;
    }
    
    a:hover, button:hover, .list-group-item-action:hover {
        transform: none !important;
        box-shadow: none !important;
        background-color: inherit !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">Welcome to AI Recorder Agent</h2>
                <p class="lead">Record, transcribe, and chat with your meetings using AI.</p>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-start mt-4">
                    <a href="{{ url_for('record_meeting') }}" class="btn btn-primary px-4 me-md-2">
                        <i class="fas fa-microphone me-2"></i> Start Recording
                    </a>
                    <a href="{{ url_for('meetings_list') }}" class="btn btn-outline-secondary px-4">
                        <i class="fas fa-list me-2"></i> View Meetings
                    </a>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">Recent Meetings</h3>
                <a href="{{ url_for('meetings_list') }}" class="btn btn-sm btn-outline-secondary">View All</a>
            </div>
            <div class="card-body">
                {% if meetings %}
                <div class="list-group">
                    {% for meeting in meetings %}
                    <a href="{{ url_for('meeting_detail', meeting_id=meeting[0]) }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ meeting[1] }}</h5>
                            <small class="text-muted">{{ meeting[2] }}</small>
                        </div>
                        <div class="d-flex justify-content-between">
                            <p class="mb-1 text-muted">ID: {{ meeting[0] }}</p>
                            <div>
                                <a href="{{ url_for('chat', meeting_id=meeting[0]) }}" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-comments"></i> Chat
                                </a>
                            </div>
                        </div>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    No meetings recorded yet. Click "Start Recording" to create your first meeting!
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">Upload Recording</h4>
            </div>
            <div class="card-body">
                <form id="upload-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="title" class="form-label">Meeting Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="audio" class="form-label">Audio File</label>
                        <input type="file" class="form-control" id="audio" name="audio" accept="audio/*" required>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-2"></i> Upload & Process
                    </button>
                </form>
                <div id="upload-progress" class="progress mt-3 d-none">
                    <div class="progress-bar progress-bar-striped" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">Quick Tips</h4>
            </div>
            <div class="card-body p-0">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item">
                        <i class="fas fa-microphone text-primary me-2"></i> Record meetings with a single click
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-file-alt text-primary me-2"></i> Automatic transcription with AI
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-comments text-primary me-2"></i> Chat about past meetings
                    </li>
                    <li class="list-group-item">
                        <i class="fas fa-search text-primary me-2"></i> Search through all your meetings
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#upload-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(this);
            
            $('#upload-progress').removeClass('d-none');
            
            $.ajax({
                url: '{{ url_for("upload_audio") }}',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhr: function() {
                    var xhr = $.ajaxSettings.xhr();
                    if (xhr.upload) {
                        xhr.upload.addEventListener('progress', function(e) {
                            if (e.lengthComputable) {
                                var percent = Math.round((e.loaded / e.total) * 100);
                                $('#upload-progress .progress-bar').css('width', percent + '%');
                            }
                        }, false);
                    }
                    return xhr;
                },
                success: function(data) {
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    }
                },
                error: function(xhr) {
                    var error = 'Upload failed.';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        error = xhr.responseJSON.error;
                    }
                    alert(error);
                    $('#upload-progress').addClass('d-none');
                }
            });
        });
    });
</script>
{% endblock %}