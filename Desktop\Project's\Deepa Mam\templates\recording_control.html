{% extends "base.html" %}

{% block title %}Recording Control{% endblock %}

{% block content %}
<div class="row justify-content-center" style="background-color: #121212; min-height: 100vh; padding: 20px 0;">
    <div class="col-md-8">
        <div class="card shadow-sm" style="background-color: #1E1E1E;">
            <div class="card-header d-flex justify-content-between align-items-center" style="border-bottom: 1px solid #333;">
                <h2 class="mb-0" style="color: #FFF;">
                    {% if recording %}
                    <span class="recording-indicator"></span> Recording in Progress
                    {% else %}
                    Recording Control
                    {% endif %}
                </h2>
                <div>
                    <a href="{{ url_for('index') }}" class="btn btn-sm btn-outline-secondary" style="border-color: #FF8C00; color: #FF8C00;">
                        Back to Home
                    </a>
                </div>
            </div>
            <div class="card-body text-center" style="color: #FFF;">
                {% if recording %}
                <div class="mb-4">
                    <div class="display-1 text-danger mb-3">
                        <i class="fas fa-microphone-alt"></i>
                    </div>
                    
                    <div class="timer-display h2" id="timer" style="color: #FF8C00;">00:00:00</div>
                    
                    <div class="mt-3">
                        <div class="alert alert-warning" style="background-color: #332900; border-color: #665200; color: #FFC107;">
                            <i class="fas fa-info-circle me-2"></i>
                            Your meeting is being recorded. Click "Stop Recording" when you're done.
                        </div>
                    </div>
                </div>
                
                <form action="{{ url_for('stop_recording') }}" method="post" class="d-grid gap-2">
                    <button type="submit" class="btn btn-danger btn-lg" style="background-color: #FF8C00; border-color: #FF8C00;">
                        <i class="fas fa-stop-circle me-2"></i> Stop Recording
                    </button>
                </form>
                {% else %}
                <div class="alert alert-info" style="background-color: #003333; border-color: #006666; color: #00CCCC;">
                    <i class="fas fa-info-circle me-2"></i>
                    No active recording. Start a new recording from the main menu.
                </div>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('record_meeting') }}" class="btn btn-primary btn-lg" style="background-color: #FF8C00; border-color: #FF8C00;">
                        <i class="fas fa-microphone me-2"></i> Start New Recording
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    {% if recording %}
    <div class="col-md-4">
        <div class="card shadow-sm" style="background-color: #1E1E1E;">
            <div class="card-header" style="border-bottom: 1px solid #333; color: #FFF;">
                <h3 class="mb-0">Recording Tips</h3>
            </div>
            <div class="card-body" style="color: #FFF;">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item" style="background-color: #1E1E1E; color: #FFF; border-color: #333;">
                        <i class="fas fa-volume-up me-2" style="color: #FF8C00;"></i> Ensure speakers are clear
                    </li>
                    <li class="list-group-item" style="background-color: #1E1E1E; color: #FFF; border-color: #333;">
                        <i class="fas fa-user-tag me-2" style="color: #FF8C00;"></i> Identify yourself before speaking
                    </li>
                    <li class="list-group-item" style="background-color: #1E1E1E; color: #FFF; border-color: #333;">
                        <i class="fas fa-tasks me-2" style="color: #FF8C00;"></i> Summarize action items
                    </li>
                    <li class="list-group-item" style="background-color: #1E1E1E; color: #FFF; border-color: #333;">
                        <i class="fas fa-bell me-2" style="color: #FF8C00;"></i> Don't leave this page until done
                    </li>
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
{% if recording %}
<script>
    // Timer functionality
    $(document).ready(function() {
        let seconds = 0;
        let timerElement = document.getElementById('timer');
        
        function updateTimer() {
            seconds++;
            
            let hrs = Math.floor(seconds / 3600);
            let mins = Math.floor((seconds % 3600) / 60);
            let secs = seconds % 60;
            
            // Format as HH:MM:SS
            let timeStr = 
                (hrs < 10 ? "0" : "") + hrs + ":" + 
                (mins < 10 ? "0" : "") + mins + ":" + 
                (secs < 10 ? "0" : "") + secs;
            
            timerElement.textContent = timeStr;
        }
        
        // Start the timer
        let timerInterval = setInterval(updateTimer, 1000);
        
        // Stop timer when leaving page
        window.addEventListener('beforeunload', function() {
            clearInterval(timerInterval);
        });
        
        // Check if recording is still active every 10 seconds
        function checkRecordingStatus() {
            $.getJSON('{{ url_for("recording_control") }}?check=1', function(data) {
                if (!data.recording) {
                    window.location.reload();
                }
            });
        }
        
        setInterval(checkRecordingStatus, 10000);
    });
</script>
{% endif %}
{% endblock %}