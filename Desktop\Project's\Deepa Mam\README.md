# 🎙️ VideoListiner

<div align="center">
  <img src="https://images.unsplash.com/photo-1551847677-dc82d764e1eb?q=80&w=1400&auto=format&fit=crop" alt="VideoListiner Banner" width="800"/>
  
  <p align="center">
    <strong>An AI-powered meeting recording, transcription, and analysis tool</strong>
  </p>
  
  <p align="center">
    <a href="#features">Features</a> •
    <a href="#installation">Installation</a> •
    <a href="#setup">Setup</a> •
    <a href="#usage">Usage</a> •
    <a href="#advanced-features">Advanced Features</a> •
    <a href="#troubleshooting">Troubleshooting</a>
  </p>
  
  <p align="center">
    <img src="https://img.shields.io/badge/Python-3.8+-blue.svg" alt="Python 3.8+"/>
    <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License: MIT"/>
    <img src="https://img.shields.io/badge/AI%20Powered-Whisper-orange.svg" alt="AI Powered: Whisper"/>
  </p>
</div>

## 📋 Overview

VideoListiner is a powerful meeting assistant that records, transcribes, and helps you extract insights from your meetings. Using advanced AI, it enables natural language conversations with your meeting recordings, generates summaries, and facilitates topic exploration.

<img src="https://images.unsplash.com/photo-**********-74b037a25d84?q=80&w=1200&auto=format&fit=crop" alt="Meeting Transcription" width="600"/>

## ✨ Features

<div align="center">
  <table>
    <tr>
      <td align="center">🎤</td>
      <td><strong>Comprehensive Recording</strong></td>
      <td>Captures both system audio and microphone input for complete meeting coverage</td>
    </tr>
    <tr>
      <td align="center">📝</td>
      <td><strong>Accurate Transcription</strong></td>
      <td>Converts speech to text with high accuracy using OpenAI's Whisper model</td>
    </tr>
    <tr>
      <td align="center">🤖</td>
      <td><strong>AI-Generated Summaries</strong></td>
      <td>Automatically creates concise meeting summaries with key points and action items</td>
    </tr>
    <tr>
      <td align="center">💬</td>
      <td><strong>Interactive Chat</strong></td>
      <td>Ask questions about your meetings in natural language and get instant answers</td>
    </tr>
    <tr>
      <td align="center">🔍</td>
      <td><strong>Topic Exploration</strong></td>
      <td>Deep-dive into specific topics mentioned during meetings for better understanding</td>
    </tr>
    <tr>
      <td align="center">🔎</td>
      <td><strong>Searchable Database</strong></td>
      <td>Find past meetings and information quickly with powerful search capabilities</td>
    </tr>
    <tr>
      <td align="center">🖥️</td>
      <td><strong>Intuitive Interface</strong></td>
      <td>Modern dark-themed GUI designed for easy navigation and accessibility</td>
    </tr>
  </table>
</div>

## 🔧 Installation

### Prerequisites

- **Python 3.8+** installed on your system
- **FFmpeg** for audio processing

### Dependencies Setup

1. **Install FFmpeg**
   - **Windows**: Download from [FFmpeg.org](https://ffmpeg.org/download.html) and add to PATH
   - **macOS**: `brew install ffmpeg`
   - **Linux**: `apt-get install ffmpeg`

2. **Install Python Packages**
   ```bash
   pip install -r requirements.txt
   ```

## 🎛️ Setup

### Setting Up System Audio Recording

#### Windows:
1. Enable "Stereo Mix" in Windows sound settings:
   <details>
   <summary>Click to expand instructions</summary>
   
   - Right-click the speaker icon in the taskbar
   - Select "Sounds"
   - Go to the "Recording" tab
   - Right-click in the empty area and check "Show Disabled Devices"
   - Right-click on "Stereo Mix" and select "Enable"
   </details>

2. If Stereo Mix is not available:
   <details>
   <summary>Click to expand alternative</summary>
   
   - Install [VB-Cable](https://vb-audio.com/Cable/)
   - Configure your applications to output audio to the VB-Cable
   - Select "VB-Cable Output" as your recording device in VideoListiner
   </details>

#### macOS:
<details>
<summary>Click for macOS setup</summary>

1. Install [BlackHole](https://existential.audio/blackhole/) or [Loopback](https://rogueamoeba.com/loopback/)
2. Configure as a multi-output device with your speakers
3. Select BlackHole/Loopback as your recording input in VideoListiner
</details>

## 🚀 Usage

1. **Launch the application**
   ```bash
   python app_gui.py
   ```

2. **Record a Meeting**
   - Select your audio input device
   - Enter a meeting title
   - Click "Start Recording"
   - When finished, click "Stop Recording"

3. **After Recording**
   - The application automatically processes the audio
   - View the transcript and summary in the meeting details page
   - Chat with your meeting by clicking "Chat with this Meeting"

<img src="https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?q=80&w=1200&auto=format&fit=crop" alt="VideoListiner Interface" width="600"/>

## 🔍 Advanced Features

### 💬 Chat with Meeting Recordings

Access AI-powered conversations with your meeting content:

1. Navigate to "Meetings" in the main interface
2. Select a meeting from the list
3. Click "Chat" button
4. Ask questions like:
   - "What decisions were made about the marketing budget?"
   - "Who was responsible for the Q3 report?"
   - "Summarize the discussion on product features"

### 🧠 Topic Exploration

Gain deeper understanding of concepts mentioned in meetings:

- During chat, type `/explore [topic]` to research specific topics
- Example: `/explore machine learning algorithms`
- The AI provides contextualized information even if not covered in detail during the meeting

### ⌨️ Special Chat Commands

| Command | Description |
|---------|-------------|
| `summary` | Generate an AI summary of the meeting |
| `transcript` | View the complete meeting transcript |
| `/explore [topic]` | Get in-depth information about a specific topic |
| `actions` | List all action items identified in the meeting |
| `decisions` | Show key decisions made during the meeting |

## 🔧 Troubleshooting

<details>
<summary><b>FFmpeg Missing Error</b></summary>
<p>If you see "FFmpeg not found" in logs:</p>
<ul>
  <li>Ensure FFmpeg is installed and added to your system PATH</li>
  <li>Restart the application after installing FFmpeg</li>
  <li>The app will attempt to download FFmpeg automatically if not found</li>
</ul>
</details>

<details>
<summary><b>Audio Device Issues</b></summary>
<ul>
  <li>Try different input devices from the dropdown menu</li>
  <li>Make sure your selected device is not being used by another application</li>
  <li>Check that system permissions allow the application to access your microphone</li>
</ul>
</details>

<details>
<summary><b>Transcription Problems</b></summary>
<ul>
  <li>For better performance on slower machines, select the "tiny" Whisper model in Settings</li>
  <li>Ensure the audio quality is clear - background noise can affect transcription</li>
  <li>For long recordings, the transcription may take several minutes to complete</li>
</ul>
</details>

<details>
<summary><b>Dark Mode Display Issues</b></summary>
<ul>
  <li>If text is not visible in dark mode, try updating to the latest version</li>
  <li>Adjust your system's display settings to ensure proper contrast</li>
</ul>
</details>

## 💻 Command Line Usage

For users who prefer the command line interface:

```bash
python app.py
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

<div align="center">
  <p>Made with ❤️ by VideoListiner Team</p>
  <a href="https://github.com/yourusername/VideoListiner">GitHub</a> •
  <a href="#documentation">Documentation</a> •
  <a href="#contributing">Contributing</a>
</div>
