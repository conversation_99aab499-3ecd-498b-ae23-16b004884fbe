{% extends "base.html" %}

{% block title %}Chat - {{ meeting[1] }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #FF8C00;  /* Dark Orange */
        --primary-dark: #E67E00;
        --bg-dark: #121212;
        --card-dark: #1E1E1E;
        --text-primary: #E0E0E0;
        --text-secondary: #B0B0B0;
        --border-color: #333333;
        --ai-gradient: linear-gradient(135deg, #2a2a2a, #1E1E1E);
        --user-gradient: linear-gradient(135deg, rgba(255, 140, 0, 0.2), rgba(230, 126, 0, 0.3));
        --system-gradient: linear-gradient(135deg, #1c1c1c, #262626);
    }

    body {
        background-color: var(--bg-dark);
        color: var(--text-primary);
    }

    .chat-container {
        height: 70vh;
        display: flex;
        flex-direction: column;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }
    
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background-color: var(--card-dark);
        background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23222222' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        border-radius: 0;
        border: 1px solid var(--border-color);
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) var(--card-dark);
    }
    
    .chat-messages::-webkit-scrollbar {
        width: 6px;
    }
    
    .chat-messages::-webkit-scrollbar-track {
        background: var(--card-dark);
    }
    
    .chat-messages::-webkit-scrollbar-thumb {
        background-color: var(--primary-dark);
        border-radius: 6px;
    }
    
    .message {
        margin-bottom: 18px;
        padding: 14px 18px;
        border-radius: 18px;
        max-width: 85%;
        animation: fadeIn 0.3s ease-in-out;
        position: relative;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .user-message {
        background-image: var(--user-gradient);
        color: var(--text-primary);
        margin-left: auto;
        border-bottom-right-radius: 5px;
        border: 1px solid rgba(255, 140, 0, 0.3);
    }
    
    .ai-message {
        background-image: var(--ai-gradient);
        color: var(--text-primary);
        margin-right: auto;
        border-bottom-left-radius: 5px;
        border: 1px solid var(--border-color);
    }
    
    .system-message {
        background-image: var(--system-gradient);
        color: var(--primary-color);
        margin: 0 auto;
        text-align: center;
        font-size: 0.9rem;
        border: 1px solid var(--border-color);
        max-width: 70%;
        border-radius: 12px;
    }
    
    .message-time {
        font-size: 0.7rem;
        color: var(--text-secondary);
        margin-top: 8px;
        text-align: right;
    }
    
    .chat-input {
        margin-top: 15px;
        position: relative;
    }
    
    .chat-input .form-control {
        border-radius: 20px;
        padding-right: 50px;
        background-color: #2a2a2a;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) inset;
        color: var(--text-primary);
        padding: 12px 20px;
    }
    
    .chat-input .form-control:focus {
        border-color: var(--primary-dark);
        box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.2);
    }

    .form-control::placeholder {
        color: #999;
    }
    
    .loading-indicator {
        display: none;
        position: absolute;
        right: 65px;
        top: 13px;
    }
    
    .meeting-info {
        font-size: 0.9rem;
        color: var(--text-primary);
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        line-height: 1.4;
    }
    
    .meeting-info strong {
        color: var(--primary-color);
        margin-right: 5px;
    }
    
    .meeting-info i {
        width: 20px;
        text-align: center;
        color: var(--primary-color);
        margin-right: 8px;
    }
    
    .command-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        overflow-x: auto;
        padding: 10px 0;
        scrollbar-width: thin;
        scrollbar-color: var(--primary-color) transparent;
    }
    
    .command-buttons::-webkit-scrollbar {
        height: 6px;
    }
    
    .command-buttons::-webkit-scrollbar-track {
        background: transparent;
    }
    
    .command-buttons::-webkit-scrollbar-thumb {
        background-color: var(--primary-dark);
        border-radius: 6px;
    }
    
    .command-btn {
        white-space: nowrap;
        background-color: rgba(42, 42, 42, 0.7);
        border: 1px solid var(--border-color);
        color: var(--text-primary);
        border-radius: 15px;
        padding: 5px 12px;
        transition: all 0.2s ease-in-out !important;
    }
    
    .command-btn:hover {
        background-color: rgba(51, 51, 51, 0.7) !important;
        color: var(--primary-color) !important;
        border-color: var(--primary-color) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 0 8px rgba(255, 140, 0, 0.4) !important;
    }
    
    .card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        border: 1px solid var(--border-color);
        background-color: var(--card-dark);
    }
    
    .card-body {
        background-color: var(--card-dark);
        color: var(--text-primary);
    }
    
    .card-header {
        background-color: #1a1a1a;
        border-bottom: 2px solid #333;
        padding: 15px 20px;
    }

    .card-header h4, .card-header h3 {
        color: var(--text-primary);
    }
    
    .btn-primary {
        border-radius: 20px;
        background-color: var(--primary-color);
        border-color: var(--primary-dark);
    }
    
    .card ul.small {
        padding-left: 1.5rem;
        margin-bottom: 0;
        color: var(--text-primary);
    }
    
    .card ul.small li {
        margin-bottom: 8px;
        position: relative;
    }
    
    .card ul.small li::marker {
        color: var(--primary-color);
    }

    .summary-container {
        background-color: #1a1a1a;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid var(--primary-color);
    }
    
    .summary-section {
        font-weight: 600;
        color: var(--primary-color);
        margin: 15px 0 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #333;
    }
    
    .summary-section-icon {
        margin-right: 8px;
        color: var(--primary-color);
    }
    
    .summary-list {
        list-style-type: none;
        padding-left: 10px;
        margin-bottom: 15px;
    }
    
    .summary-list li {
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
    }
    
    .summary-list li:before {
        content: "•";
        position: absolute;
        left: 0;
        color: var(--primary-color);
    }

    .summary-container p {
        color: var(--text-primary);
    }

    @media (max-width: 768px) {
        .chat-container {
            height: 60vh;
        }
        
        .command-buttons {
            flex-wrap: wrap;
        }
        
        .message {
            max-width: 90%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0">
                    <i class="fas fa-comments me-2"></i> Chat with: {{ meeting[1] }}
                </h3>
                <div>
                    <a href="{{ url_for('meeting_detail', meeting_id=meeting[0]) }}" class="btn btn-sm btn-outline-secondary">
                        View Details
                    </a>
                </div>
            </div>
            <div class="card-body chat-container">
                <div class="command-buttons">
                    <button class="btn btn-sm command-btn" data-command="summary">
                        <i class="fas fa-list-alt me-1"></i> Get Summary
                    </button>
                    <button class="btn btn-sm command-btn" data-command="transcript">
                        <i class="fas fa-file-alt me-1"></i> View Transcript
                    </button>
                    <button class="btn btn-sm command-btn" data-command="topics">
                        <i class="fas fa-tags me-1"></i> Key Topics
                    </button>
                    <button class="btn btn-sm command-btn" data-command="decisions">
                        <i class="fas fa-check-circle me-1"></i> Decisions
                    </button>
                    <button class="btn btn-sm command-btn" data-command="actions">
                        <i class="fas fa-tasks me-1"></i> Action Items
                    </button>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="message system-message">
                        Chat started. Ask questions about this meeting.
                    </div>
                </div>
                
                <form id="chat-form" class="chat-input">
                    <div class="input-group">
                        <input type="text" id="message-input" class="form-control" placeholder="Ask about this meeting..." required>
                        <div class="loading-indicator" id="loading">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        </div>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>Meeting Info</h4>
            </div>
            <div class="card-body">
                <p class="meeting-info">
                    <i class="fas fa-calendar-alt"></i>
                    <span><strong>Date:</strong> {{ meeting[2] }}</span>
                </p>
                <p class="meeting-info">
                    <i class="fas fa-clock"></i>
                    <span><strong>Duration:</strong>
                    {% if meeting[2] and meeting[3] %}
                        {% if meeting[2] is string and meeting[3] is string %}
                            Duration calculated during playback
                        {% else %}
                            {% set start_time = meeting[2] %}
                            {% set end_time = meeting[3] %}
                            {% set duration_minutes = ((end_time - start_time).total_seconds() / 60)|int %}
                            {{ duration_minutes }} minutes
                        {% endif %}
                    {% else %}
                        Unknown duration
                    {% endif %}
                    </span>
                </p>
                {% if meeting[4] %}
                <p class="meeting-info">
                    <i class="fas fa-users"></i>
                    <span><strong>Participants:</strong> {{ meeting[4] }}</span>
                </p>
                {% endif %}
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips</h4>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>Ask specific questions about the meeting</li>
                    <li>Use commands above for quick answers</li>
                    <li>Include "explain" for detailed responses</li>
                    <li>Questions will use both meeting context and general knowledge</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        const chatMessages = $('#chat-messages');
        const chatForm = $('#chat-form');
        const messageInput = $('#message-input');
        const loading = $('#loading');
        const meetingId = {{ meeting[0] }};
        
        function scrollToBottom() {
            chatMessages.scrollTop(chatMessages[0].scrollHeight);
        }
        
        function formatTime() {
            const now = new Date();
            return now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }
        
        function addMessage(content, isUser = false) {
            const messageClass = isUser ? 'user-message' : 'ai-message';
            const messageHtml = `
                <div class="message ${messageClass}">
                    <div>${content}</div>
                    <div class="message-time">${formatTime()}</div>
                </div>
            `;
            chatMessages.append(messageHtml);
            scrollToBottom();
        }
        
        function formatSummary(summaryText) {
            // First, remove any markdown formatting
            let cleanText = summaryText.replace(/\*\*/g, ''); // Remove bold markers
            cleanText = cleanText.replace(/\*/g, ''); // Remove any remaining asterisks
            
            // Split by lines and process
            let lines = cleanText.split('\n');
            let html = '<div class="summary-container">';
            let inList = false;
            
            for(let i = 0; i < lines.length; i++) {
                let line = lines[i].trim();
                
                if(!line) {
                    // Empty line
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += '<br>';
                } 
                else if(line.startsWith('Key Discussion Points:')) {
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h5 class="summary-section"><i class="fas fa-comment-dots summary-section-icon"></i>${line}</h5>`;
                }
                else if(line.startsWith('Decisions Made:')) {
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h5 class="summary-section"><i class="fas fa-check-circle summary-section-icon"></i>${line}</h5>`;
                }
                else if(line.startsWith('Action Items:')) {
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h5 class="summary-section"><i class="fas fa-tasks summary-section-icon"></i>${line}</h5>`;
                }
                else if(line.startsWith('Important Dates/Deadlines:')) {
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h5 class="summary-section"><i class="fas fa-calendar-alt summary-section-icon"></i>${line}</h5>`;
                }
                else if(line.startsWith('- ') || line.startsWith('• ')) {
                    // List item
                    if(!inList) {
                        html += '<ul class="summary-list">';
                        inList = true;
                    }
                    let itemText = line.substring(2).trim();
                    html += `<li>${itemText}</li>`;
                }
                else {
                    // Regular paragraph
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<p>${line}</p>`;
                }
            }
            
            // Close list if still open
            if(inList) {
                html += '</ul>';
            }
            
            html += '</div>';
            return html;
        }
        
        function askQuestion(question) {
            addMessage(question, true);
            loading.show();
            
            // Special commands
            if (question.toLowerCase() === 'summary') {
                $.getJSON(`/api/summary/${meetingId}`, function(data) {
                    if (data.summary) {
                        const formattedSummary = formatSummary(data.summary);
                        addMessage(formattedSummary);
                    } else {
                        addMessage("No summary available for this meeting.");
                    }
                    loading.hide();
                }).fail(function() {
                    addMessage("Error generating summary. Please try again.");
                    loading.hide();
                });
                return;
            }
            
            if (question.toLowerCase() === 'transcript') {
                const transcript = {{ meeting[5]|tojson|safe }};
                addMessage('<strong>Meeting Transcript:</strong><br><pre>' + transcript + '</pre>');
                loading.hide();
                return;
            }
            
            // Regular question
            $.ajax({
                url: `/api/ask/${meetingId}`,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({question: question}),
                success: function(data) {
                    addMessage(data.answer || "No answer available");
                    loading.hide();
                },
                error: function() {
                    addMessage("Sorry, I couldn't process your question. Please try again.");
                    loading.hide();
                }
            });
        }
        
        // Handle form submit
        chatForm.on('submit', function(e) {
            e.preventDefault();
            const question = messageInput.val().trim();
            if (question) {
                askQuestion(question);
                messageInput.val('');
            }
        });
        
        // Handle command buttons
        $('.command-btn').on('click', function() {
            const command = $(this).data('command');
            let question;
            
            switch(command) {
                case 'summary':
                    question = 'summary';
                    break;
                case 'transcript':
                    question = 'transcript';
                    break;
                case 'topics':
                    question = 'What were the key topics discussed in this meeting?';
                    break;
                case 'decisions':
                    question = 'What decisions were made during this meeting?';
                    break;
                case 'actions':
                    question = 'What action items were assigned in this meeting?';
                    break;
                default:
                    return;
            }
            
            askQuestion(question);
        });
        
        scrollToBottom();
    });
</script>
{% endblock %}