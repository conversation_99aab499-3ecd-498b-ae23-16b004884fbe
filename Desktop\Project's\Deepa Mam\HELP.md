# Meeting Recorder Help Guide

## What is a Meeting ID?

A **Meeting ID** is a unique number assigned to each meeting when it's recorded and saved to the database. This ID is used to identify specific meetings for searching, chatting, and retrieval.

### How to Find Meeting IDs:

1. **When recording a new meeting**: 
   - After stopping a recording, the application displays: `Meeting recorded with ID: [number]`
   - This number is your meeting ID

2. **Listing past meetings**:
   - From the main menu, select option **2. List past meetings**
   - You'll see a list with format: `ID: [number] - [meeting title] ([date/time])`
   - The number after "ID:" is your meeting ID

3. **After searching**:
   - When you search meetings using option 4, results show: `Meeting [number]: [title]...`
   - The number displayed is the meeting ID

## How to Chat with Past Meetings

### Starting a Chat Session:

1. From the main menu, select option **3. Chat with a past meeting**
2. Enter the meeting ID when prompted
3. The chat interface will load, showing meeting details

### Chat Commands:

During a chat session, you can use these special commands:

- `summary` - Generate an AI summary of the meeting
- `transcript` - Display the full meeting transcript
- `exit` or `quit` or `q` - End the chat session and return to main menu

### Asking Questions:

Simply type your question and press Enter. Examples:
- "What were the main topics discussed?"
- "Did anyone mention the University of America?"
- "What decisions were made in this meeting?"
- "When is the next meeting scheduled?"

The AI will analyze the transcript and respond based on the meeting content.

### Chat History:

All your conversations are saved in the database and linked to the specific meeting.
This allows you to reference previous discussions.

## Example Workflow:

1. Record a meeting (option 1)
2. Note the meeting ID when it's saved
3. Later, list past meetings (option 2) to find the ID
4. Select chat with past meeting (option 3)
5. Enter the meeting ID
6. Ask questions about the meeting content
7. Type 'summary' to get an overview
8. Type 'exit' to return to the main menu
