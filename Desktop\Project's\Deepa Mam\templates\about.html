{% extends "base.html" %}

{% block title %}About - AI Recorder Agent{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #FF8C00;  /* Dark Orange */
        --primary-dark: #E67E00;
        --bg-dark: #121212;
        --card-dark: #1E1E1E;
        --text-primary: #E0E0E0;
        --text-secondary: #B0B0B0;
        --border-color: #333333;
        --success-color: #4CAF50;
    }
    
    body {
        background-color: var(--bg-dark);
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        color: var(--text-primary);
    }
    
    /* Hero section */
    .hero-section {
        background: linear-gradient(135deg, var(--primary-dark) 0%, #A05A00 100%);
        color: white;
        padding: 5rem 0;
        border-radius: 0.5rem;
        margin-bottom: 3rem;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
    }
    
    .hero-section p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 800px;
        margin: 0 auto;
    }
    
    /* Feature cards */
    .feature-card {
        height: 100%;
        background-color: var(--card-dark);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .feature-card .card-header {
        background-color: rgba(255, 140, 0, 0.1);
        border-bottom: 1px solid var(--border-color);
        padding: 1.25rem;
    }
    
    .feature-card .card-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }
    
    .feature-card .card-title {
        font-weight: 600;
        margin-bottom: 0;
        color: var(--primary-color);
    }
    
    /* Section styling */
    .section-heading {
        position: relative;
        margin-bottom: 2.5rem;
        text-align: center;
    }
    
    .section-heading:after {
        content: '';
        display: block;
        width: 80px;
        height: 4px;
        background: var(--primary-color);
        margin: 1rem auto 0;
        border-radius: 2px;
    }
    
    /* Technology section */
    .tech-list {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1.5rem;
        justify-content: center;
    }
    
    .tech-badge {
        background: #2E2E2E;
        border-radius: 30px;
        padding: 0.5rem 1rem;
        color: var(--text-primary);
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        border: 1px solid var(--border-color);
    }
    
    .tech-badge i {
        margin-right: 0.5rem;
        color: var(--primary-color);
    }
    
    /* Workflow */
    .workflow {
        position: relative;
        padding-left: 70px;
        margin-bottom: 2.5rem;
    }
    
    .workflow-step {
        position: relative;
        margin-bottom: 2.5rem;
    }
    
    .workflow-step:last-child {
        margin-bottom: 0;
    }
    
    .workflow-step .step-number {
        position: absolute;
        left: -70px;
        top: 0;
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        color: var(--bg-dark);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.5rem;
    }
    
    .workflow-step h4 {
        margin-bottom: 1rem;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .workflow-step p {
        color: var(--text-secondary);
    }
    
    .workflow-connect {
        position: absolute;
        left: -46px;
        top: 50px;
        bottom: 20px;
        width: 2px;
        background: var(--border-color);
        z-index: -1;
    }
    
    /* Tables */
    .tech-table {
        border-collapse: separate;
        border-spacing: 0;
        border-radius: 0.5rem;
        overflow: hidden;
        width: 100%;
        margin-bottom: 2rem;
    }
    
    .tech-table thead {
        background: var(--primary-color);
        color: var(--bg-dark);
    }
    
    .tech-table th,
    .tech-table td {
        padding: 1rem;
        border: 1px solid var(--border-color);
    }
    
    .tech-table tbody tr {
        background-color: var(--card-dark);
    }
    
    .tech-table tbody tr:nth-child(even) {
        background-color: #2A2A2A;
    }
    
    /* Output files */
    .file-box {
        background: #2E2E2E;
        border-radius: 0.5rem;
        padding: 1.25rem;
        margin-bottom: 1rem;
        border-left: 4px solid var(--primary-color);
        display: flex;
        align-items: center;
    }
    
    .file-box .file-icon {
        font-size: 2rem;
        margin-right: 1rem;
        color: var(--primary-color);
    }
    
    .file-box .file-info {
        flex: 1;
    }
    
    .file-box .file-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text-primary);
    }
    
    .file-box .file-desc {
        margin-bottom: 0;
        color: var(--text-secondary);
    }
    
    /* Call to action */
    .cta-section {
        background: linear-gradient(135deg, var(--primary-dark) 0%, #8B4500 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 3rem;
        margin-top: 3rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .cta-section h2 {
        font-weight: 700;
        margin-bottom: 1.5rem;
    }
    
    /* Lists */
    .list-unstyled li {
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
    }
    
    .list-unstyled i {
        color: var(--success-color);
        margin-right: 0.5rem;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .hero-section {
            padding: 3rem 0;
        }
        
        .hero-section h1 {
            font-size: 2rem;
        }
        
        .hero-section p {
            font-size: 1rem;
        }
        
        .workflow {
            padding-left: 50px;
        }
        
        .workflow-step .step-number {
            left: -50px;
            width: 40px;
            height: 40px;
            font-size: 1.25rem;
        }
        
        .workflow-connect {
            left: -31px;
        }
        
        .tech-list {
            justify-content: flex-start;
        }
    }
    
    /* Remove all hover effects */
    a, button, .feature-card, .list-group-item-action {
        transition: none !important;
    }
    
    a:hover, button:hover, .feature-card:hover, .list-group-item-action:hover {
        transform: none !important;
        box-shadow: none !important;
        background-color: inherit !important;
        color: inherit !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section text-center">
    <div class="container">
        <h1>AI-Powered Meeting Recorder</h1>
        <p class="lead">
            A revolutionary tool that records, transcribes, and analyzes your meetings using cutting-edge AI technologies.
            Get insightful summaries, search through transcripts, and interact with your meeting data like never before.
        </p>
    </div>
</div>

<!-- Introduction Section -->
<section class="mb-5">
    <div class="container">
        <div class="section-heading">
            <h2>Transforming How We Document Meetings</h2>
        </div>
        <div class="row">
            <div class="col-lg-8 offset-lg-2">
                <p class="text-center mb-4">
                    AI Recorder Agent is a Python-based application designed to record, transcribe, and analyze meetings
                    using cutting-edge AI technologies. The system integrates speech-to-text transcription, natural language
                    processing (NLP), and generative AI to provide intelligent meeting summaries, searchable transcripts,
                    and interactive chat-based querying.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="mb-5">
    <div class="container">
        <div class="section-heading">
            <h2>Key Features & Capabilities</h2>
        </div>
        
        <div class="row g-4">
            <!-- Feature 1 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-microphone-alt card-icon"></i>
                        <h3 class="card-title">Audio Recording</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>High-quality audio capture from multiple sources</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Support for system audio and microphone input</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Automatic file storage with timestamps</li>
                            <li><i class="fas fa-check-circle me-2"></i>Lossless WAV format for pristine audio quality</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Feature 2 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-file-alt card-icon"></i>
                        <h3 class="card-title">Automatic Transcription</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Real-time speech-to-text conversion</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>OpenAI's Whisper for local transcription</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Support for multiple languages</li>
                            <li><i class="fas fa-check-circle me-2"></i>Persistent storage in database and text files</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Feature 3 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-brain card-icon"></i>
                        <h3 class="card-title">AI-Powered Analysis</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Google Gemini for intelligent summarization</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Key point extraction and action items</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Context-aware Q&A capabilities</li>
                            <li><i class="fas fa-check-circle me-2"></i>Detailed explanations of complex topics</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Feature 4 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-search card-icon"></i>
                        <h3 class="card-title">Search & Retrieval</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Full-text search across all transcripts</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Highlighted keyword snippets</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Fast results with SQLite FTS5</li>
                            <li><i class="fas fa-check-circle me-2"></i>Chronological and relevance-based sorting</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Feature 5 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-comments card-icon"></i>
                        <h3 class="card-title">Interactive Chat</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Conversational interface for meeting data</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Ask natural language questions</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Follow-up queries with context retention</li>
                            <li><i class="fas fa-check-circle me-2"></i>Historical chat retention for reference</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Feature 6 -->
            <div class="col-md-6 col-lg-4">
                <div class="card feature-card">
                    <div class="card-header text-center">
                        <i class="fas fa-shield-alt card-icon"></i>
                        <h3 class="card-title">Security & Privacy</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Local processing options for privacy</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Secure data storage with SQLite</li>
                            <li class="mb-2"><i class="fas fa-check-circle me-2"></i>Optional cloud AI integration</li>
                            <li><i class="fas fa-check-circle me-2"></i>Access control for sensitive meeting data</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="mb-5">
    <div class="container">
        <div class="section-heading">
            <h2>Technologies Used</h2>
        </div>
        
        <div class="tech-list mb-4">
            <div class="tech-badge"><i class="fab fa-python"></i> Python</div>
            <div class="tech-badge"><i class="fas fa-flask"></i> Flask</div>
            <div class="tech-badge"><i class="fas fa-database"></i> SQLite</div>
            <div class="tech-badge"><i class="fas fa-microphone"></i> PyAudio</div>
            <div class="tech-badge"><i class="fas fa-film"></i> FFmpeg</div>
            <div class="tech-badge"><i class="fas fa-brain"></i> Whisper</div>
            <div class="tech-badge"><i class="fas fa-robot"></i> Google Gemini</div>
            <div class="tech-badge"><i class="fas fa-search"></i> FTS5</div>
            <div class="tech-badge"><i class="fab fa-bootstrap"></i> Bootstrap</div>
            <div class="tech-badge"><i class="fab fa-js"></i> JavaScript</div>
        </div>
        
        <div class="row">
            <!-- Audio Processing -->
            <div class="col-lg-6 mb-4">
                <h3 class="mb-3">Audio Processing</h3>
                <table class="table tech-table">
                    <thead>
                        <tr>
                            <th>Technology</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>PyAudio</strong></td>
                            <td>Captures audio streams in real-time, supporting multiple input sources</td>
                        </tr>
                        <tr>
                            <td><strong>FFmpeg</strong></td>
                            <td>Handles audio preprocessing for Whisper, with auto-download capabilities</td>
                        </tr>
                        <tr>
                            <td><strong>Wave</strong></td>
                            <td>Manages WAV file operations for high-quality audio storage</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- Speech to Text -->
            <div class="col-lg-6 mb-4">
                <h3 class="mb-3">Speech-to-Text</h3>
                <table class="table tech-table">
                    <thead>
                        <tr>
                            <th>Technology</th>
                            <th>Type</th>
                            <th>Features</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Whisper (OpenAI)</strong></td>
                            <td>Local</td>
                            <td>Offline processing, multiple model sizes</td>
                        </tr>
                        <tr>
                            <td><strong>AssemblyAI</strong></td>
                            <td>Cloud</td>
                            <td>High accuracy, fallback option</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- NLP & Database -->
            <div class="col-lg-6 mb-4">
                <h3 class="mb-3">Natural Language Processing</h3>
                <table class="table tech-table">
                    <thead>
                        <tr>
                            <th>Technology</th>
                            <th>Features</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Google Gemini</strong></td>
                            <td>Generative AI for summaries, Q&A, and explanations</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="col-lg-6 mb-4">
                <h3 class="mb-3">Database & Search</h3>
                <table class="table tech-table">
                    <thead>
                        <tr>
                            <th>Technology</th>
                            <th>Purpose</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>SQLite</strong></td>
                            <td>Stores meeting data, transcripts, and chat history</td>
                        </tr>
                        <tr>
                            <td><strong>FTS5</strong></td>
                            <td>Full-text search for fast keyword-based meeting retrieval</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="mb-5">
    <div class="container">
        <div class="section-heading">
            <h2>How It Works</h2>
        </div>
        
        <div class="row">
            <div class="col-lg-10 offset-lg-1">
                <div class="workflow">
                    <!-- Recording & Transcription Flow -->
                    <h3 class="mb-4">Recording & Transcription Flow</h3>
                    
                    <div class="workflow-connect"></div>
                    
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h4>Recording Starts</h4>
                        <p>The user initiates a recording session, selecting an audio input device (microphone or system audio). PyAudio captures audio in chunks and buffers them in memory while the meeting is in progress.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h4>Processing & Saving</h4>
                        <p>When recording stops, the system saves the audio as a WAV file with a timestamped filename. The recorded audio is then passed to the transcription engine for speech-to-text conversion.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h4>Transcription</h4>
                        <p>Whisper (or AssemblyAI as a fallback) processes the audio file and generates a text transcript. This transcript is stored in the SQLite database and also saved as a separate text file for easy access.</p>
                    </div>
                    
                    <!-- Question Answering Flow -->
                    <h3 class="mb-4 mt-5">AI-Powered Question Answering</h3>
                    
                    <div class="workflow-connect"></div>
                    
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h4>User Query Analysis</h4>
                        <p>When a user asks a question about a meeting, the system analyzes the query to determine if it requires a simple factual answer or a detailed explanation based on context.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h4>Context Retrieval</h4>
                        <p>The system retrieves the relevant meeting transcript and provides it as context to the AI model, ensuring responses are grounded in the actual meeting content.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h4>Response Generation</h4>
                        <p>Google Gemini generates a response tailored to the query type - concise answers for factual questions and expanded explanations for "why/how" questions, providing the user with precisely the information they need.</p>
                    </div>
                    
                    <!-- Search Flow -->
                    <h3 class="mb-4 mt-5">Search Functionality</h3>
                    
                    <div class="workflow-connect"></div>
                    
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h4>Query Processing</h4>
                        <p>When a user performs a search, their query is processed against the SQLite FTS5 (Full-Text Search) index for optimized text searching across all meeting transcripts.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h4>Result Ranking</h4>
                        <p>Search results are ranked by relevance, ensuring the most pertinent meetings appear at the top of the results list.</p>
                    </div>
                    
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h4>Displaying Results</h4>
                        <p>Results display meeting title, date, and snippets with highlighted keywords, allowing users to quickly identify the most relevant content before clicking through to full meeting details.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Outputs Section -->
<section class="mb-5">
    <div class="container">
        <div class="section-heading">
            <h2>Outputs & Deliverables</h2>
        </div>
        
        <div class="row">
            <div class="col-lg-8 offset-lg-2">
                <!-- WAV files -->
                <div class="file-box">
                    <div class="file-icon">
                        <i class="fas fa-file-audio"></i>
                    </div>
                    <div class="file-info">
                        <h4 class="file-name">.wav files</h4>
                        <p class="file-desc">Location: recordings/</p>
                        <p class="file-desc">Raw meeting audio in high-quality WAV format</p>
                    </div>
                </div>
                
                <!-- TXT files -->
                <div class="file-box">
                    <div class="file-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="file-info">
                        <h4 class="file-name">.txt files</h4>
                        <p class="file-desc">Location: recordings/transcripts/</p>
                        <p class="file-desc">Complete text transcripts of meetings with metadata</p>
                    </div>
                </div>
                
                <!-- Database -->
                <div class="file-box">
                    <div class="file-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="file-info">
                        <h4 class="file-name">.db (SQLite)</h4>
                        <p class="file-desc">Location: Project root</p>
                        <p class="file-desc">Comprehensive database with meetings, search index, and chat history</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<div class="cta-section">
    <div class="container">
        <h2>Ready to Transform Your Meetings?</h2>
        <p class="lead mb-4">
            Start recording, transcribing, and analyzing your meetings with AI-powered insights today.
        </p>
        <a href="{{ url_for('record_meeting') }}" class="btn btn-dark btn-lg">
            <i class="fas fa-microphone-alt me-2"></i> Start Recording
        </a>
    </div>
</div>
{% endblock %}