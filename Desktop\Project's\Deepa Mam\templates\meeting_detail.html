{% extends "base.html" %}

{% block title %}Meeting: {{ meeting[1] }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --dark-bg: #121212;
        --dark-card: #1E1E1E;
        --dark-primary: #FF8C00;
        --dark-border: #333333;
        --dark-text: #E0E0E0;
        --dark-text-secondary: #AAAAAA;
        --dark-input-bg: #2D2D2D;
        --dark-highlight: rgba(255, 140, 0, 0.3);
    }
    
    body {
        background-color: var(--dark-bg);
        color: var(--dark-text);
    }
    
    .card {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
    }
    
    .card-header {
        background-color: rgba(0, 0, 0, 0.2);
        border-bottom-color: var(--dark-border);
    }
    
    .btn-primary {
        background-color: var(--dark-primary);
        border-color: var(--dark-primary);
        color: #000000;
    }
    
    .btn-outline-primary {
        color: var(--dark-primary);
        border-color: var(--dark-primary);
    }
    
    .btn-outline-primary:focus {
        box-shadow: 0 0 0 0.25rem rgba(255, 140, 0, 0.25);
    }
    
    .btn-outline-secondary {
        color: var(--dark-text);
        border-color: #555555;
    }
    
    .btn-secondary {
        background-color: #444444;
        border-color: #555555;
    }
    
    .transcript-container {
        max-height: 400px;
        overflow-y: auto;
        background-color: rgba(0, 0, 0, 0.2);
        padding: 15px;
        border-radius: 0.25rem;
        border: 1px solid var(--dark-border);
        font-size: 0.95rem;
        line-height: 1.6;
        white-space: pre-wrap;
        word-wrap: break-word;
        color: var(--dark-text);
    }
    
    .audio-controls {
        background-color: rgba(0, 0, 0, 0.2);
        border: 1px solid var(--dark-border);
        border-radius: 0.25rem;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .meeting-info {
        margin-bottom: 0.5rem;
        color: var(--dark-text);
    }
    
    .meeting-tag {
        font-size: 0.85rem;
        padding: 0.25rem 0.5rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        display: inline-block;
        background-color: rgba(255, 140, 0, 0.2);
        border: 1px solid rgba(255, 140, 0, 0.3);
        color: var(--dark-primary);
    }
    
    .timestamp {
        color: var(--dark-text-secondary);
        font-size: 0.8rem;
        margin-right: 10px;
    }
    
    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    #transcript-search {
        max-width: 300px;
    }
    
    .highlight {
        background-color: var(--dark-highlight);
        color: var(--dark-primary);
        padding: 0.1em 0;
    }
    
    .action-buttons {
        margin-bottom: 20px;
    }
    
    .action-button {
        margin-right: 8px;
        margin-bottom: 8px;
    }
    
    .form-control, .input-group-text {
        background-color: var(--dark-input-bg);
        border-color: var(--dark-border);
        color: var(--dark-text);
    }
    
    .form-control:focus {
        background-color: var(--dark-input-bg);
        color: var(--dark-text);
        border-color: var(--dark-primary);
        box-shadow: 0 0 0 0.25rem rgba(255, 140, 0, 0.25);
    }
    
    .modal-content {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
    }
    
    .modal-header, .modal-footer {
        border-color: var(--dark-border);
    }
    
    .spinner-border.text-primary {
        color: var(--dark-primary) !important;
    }
    
    .alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
        color: #f8d7da;
        border-color: rgba(220, 53, 69, 0.3);
    }
    
    a {
        color: var(--dark-primary);
    }
    
    h2, h3, h5 {
        color: var(--dark-text);
    }
    
    audio::-webkit-media-controls-panel {
        background-color: var(--dark-card);
    }
    
    audio::-webkit-media-controls-current-time-display,
    audio::-webkit-media-controls-time-remaining-display {
        color: var(--dark-text);
    }
    
    @media (max-width: 767px) {
        .section-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        #transcript-search {
            max-width: 100%;
            margin-top: 10px;
        }
        
        .transcript-container {
            max-height: 300px;
        }
        
        .action-button {
            width: 100%;
            margin-right: 0;
        }
    }
    
    /* Enhanced Summary Styling */
    .bg-dark-orange {
        background-color: #d35400 !important;
        background-image: linear-gradient(135deg, #e67e22, #d35400);
    }
    
    /* Enhanced summary container */
    .summary-container {
        background-color: rgba(20, 20, 20, 0.7);
        border-radius: 0.75rem;
        padding: 1.25rem;
        color: var(--dark-text);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 1px solid #2c2c2c;
    }
    
    /* Enhanced section headers */
    .summary-section {
        color: #ffffff;
        font-weight: 600;
        padding: 1rem 1.25rem;
        margin: 1.25rem -1.25rem 1rem -1.25rem;
        background-color: #d35400;
        background-image: linear-gradient(135deg, #e67e22, #d35400);
        border-left: 5px solid #a04000;
        border-radius: 0 0.5rem 0.5rem 0;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .summary-section:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        transform: translateY(-2px);
    }
    
    .summary-section-icon {
        margin-right: 0.75rem;
    }
    
    /* Enhanced list styling */
    .summary-list {
        padding-left: 1.5rem;
        margin-bottom: 1.75rem;
        list-style-type: none;
        color: var(--dark-text);
    }
    
    .summary-list li {
        margin-bottom: 0.85rem;
        position: relative;
        padding-left: 1.25rem;
        line-height: 1.5;
        transition: transform 0.2s ease;
    }
    
    .summary-list li:before {
        content: "";
        position: absolute;
        left: 0;
        top: 0.5rem;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #e67e22;
    }
    
    .summary-list li:hover {
        transform: translateX(2px);
    }
    
    /* Additional styling for the summary card */
    #summary-card {
        transition: all 0.3s ease;
        overflow: hidden;
        border-radius: 8px;
    }
    
    #summary-card.show {
        animation: fadeInUp 0.5s forwards;
    }
    
    #summary-card .card-header {
        border-bottom: 3px solid #a04000;
        font-weight: bold;
        padding: 1rem 1.25rem;
    }
    
    #summary-card .card-body {
        background-color: var(--dark-card);
        border: 1px solid var(--dark-border);
        border-top: none;
        padding: 1.25rem;
    }
    
    #summary-content p {
        color: var(--dark-text-secondary);
        margin-bottom: 1rem;
    }
    
    /* Animation for summary sections */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Enhanced loading spinner */
    .summary-loading {
        padding: 2rem 1rem;
        text-align: center;
    }
    
    .summary-loading .spinner-border {
        width: 3rem;
        height: 3rem;
    }
    
    .summary-empty {
        padding: 2rem;
        text-align: center;
        color: var(--dark-text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2 class="mb-0">{{ meeting[1] }}</h2>
                <div>
                    <a href="{{ url_for('meetings_list') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Meetings
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="action-buttons">
                    <a href="{{ url_for('chat', meeting_id=meeting[0]) }}" class="btn btn-primary action-button">
                        <i class="fas fa-comments me-1"></i> Chat About This Meeting
                    </a>
                    <button id="generate-summary-btn" class="btn btn-outline-secondary action-button">
                        <i class="fas fa-list-alt me-1"></i> Generate Summary
                    </button>
                    <button id="copy-transcript-btn" class="btn btn-outline-secondary action-button">
                        <i class="fas fa-copy me-1"></i> Copy Transcript
                    </button>
                </div>
                
                {% if meeting[4] %}
                <div class="audio-controls">
                    <h5><i class="fas fa-headphones me-2"></i>Audio Recording</h5>
                    <audio id="meeting-audio" class="w-100 mb-3" controls>
                        <source src="{{ url_for('static', filename='recordings/' + meeting[4].split('/')[-1]) }}" type="audio/wav">
                        Your browser does not support the audio element.
                    </audio>
                </div>
                {% endif %}
                
                <div class="section-header">
                    <h3>Transcript</h3>
                    <div class="input-group" id="transcript-search">
                        <input type="text" id="search-input" class="form-control" placeholder="Search transcript...">
                        <button class="btn btn-outline-secondary" type="button" id="search-button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <div class="transcript-container" id="transcript-text">
                    {{ meeting[5] }}
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm mb-4" id="summary-card" style="display: none;">
            <div class="card-header bg-dark-orange text-white">
                <h3 class="mb-0"><i class="fas fa-file-alt me-2"></i> Meeting Summary</h3>
            </div>
            <div class="card-body">
                <div id="summary-content" class="mb-3">
                    <div class="summary-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading summary...</span>
                        </div>
                        <p class="mt-3">Generating comprehensive summary...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <h3 class="mb-0">Meeting Details</h3>
            </div>
            <div class="card-body">
                <p class="meeting-info">
                    <i class="fas fa-calendar-alt me-2"></i> <strong>Date:</strong>
                    {{ meeting[2] }}
                </p>
                
                <p class="meeting-info">
                    <i class="fas fa-clock me-2"></i> <strong>Duration:</strong>
                    {% if meeting[2] and meeting[3] %}
                        {% if meeting[2] is string or meeting[3] is string %}
                            {% set duration_minutes = 0 %}
                            Calculating...
                        {% else %}
                            {% set duration_minutes = ((meeting[3] - meeting[2]).total_seconds() / 60)|int %}
                            {{ duration_minutes }} minutes
                        {% endif %}
                    {% else %}
                        Unknown
                    {% endif %}
                </p>
                
                <p class="meeting-info">
                    <i class="fas fa-id-card me-2"></i> <strong>Meeting ID:</strong>
                    {{ meeting[0] }}
                </p>
                
                {% if meeting[4] %}
                <p class="meeting-info">
                    <i class="fas fa-file-audio me-2"></i> <strong>Audio File:</strong>
                    {{ meeting[4].split('/')[-1] }}
                </p>
                {% endif %}
                
                {% if meeting[5] %}
                <p class="meeting-info">
                    <i class="fas fa-file-alt me-2"></i> <strong>Transcript Length:</strong>
                    {{ meeting[5]|length }} characters
                </p>
                {% endif %}
                
                {% if meeting[4] %}
                <p class="meeting-info">
                    <i class="fas fa-users me-2"></i> <strong>Participants:</strong>
                    {% if meeting[4] %}{{ meeting[4] }}{% else %}Not specified{% endif %}
                </p>
                {% endif %}
            </div>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-header">
                <h3 class="mb-0">Actions</h3>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" type="button" id="download-transcript-btn">
                        <i class="fas fa-file-download me-1"></i> Download Transcript
                    </button>
                    
                    {% if meeting[4] %}
                    <a href="{{ url_for('static', filename='recordings/' + meeting[4].split('/')[-1]) }}" 
                       class="btn btn-outline-primary" download>
                        <i class="fas fa-file-audio me-1"></i> Download Audio
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-outline-secondary" id="share-meeting-btn">
                        <i class="fas fa-share-alt me-1"></i> Share Meeting
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">Share Meeting</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="share-link" class="form-label">Meeting Link</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="share-link" readonly 
                            value="{{ request.host_url }}meeting/{{ meeting[0] }}">
                        <button class="btn btn-outline-secondary" type="button" id="copy-link-btn">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Generate summary
        $('#generate-summary-btn').click(function() {
            $('#summary-card').show().addClass('show');
            
            $.getJSON('/api/summary/{{ meeting[0] }}', function(data) {
                // Format the summary properly as HTML
                let formattedSummary = formatSummary(data.summary);
                $('#summary-content').html(formattedSummary);
                
                // Animate the sections appearing
                setTimeout(function() {
                    $('.summary-section').each(function(index) {
                        $(this).css({
                            'animation': `fadeInUp ${0.3 + (index * 0.1)}s forwards`
                        });
                    });
                }, 100);
            }).fail(function() {
                $('#summary-content').html('<div class="alert alert-danger">Failed to generate summary. Please try again later.</div>');
            });
        });
        
        // Format the summary text into proper HTML with enhanced styling
        function formatSummary(summaryText) {
            // First, remove any markdown formatting
            let cleanText = summaryText.replace(/\*\*/g, ''); // Remove bold markers
            cleanText = cleanText.replace(/\*/g, ''); // Remove any remaining asterisks
            
            // Split by lines and process
            let lines = cleanText.split('\n');
            let html = '';
            let inList = false;
            
            for(let i = 0; i < lines.length; i++) {
                let line = lines[i].trim();
                
                if(!line) {
                    // Empty line
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += '<br>';
                } 
                else if(line.startsWith('Key Discussion Points:')) {
                    // Key Discussion Points section
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h4 class="mt-4 summary-section"><i class="fas fa-comment-dots summary-section-icon"></i>${line}</h4>`;
                }
                else if(line.startsWith('Decisions Made:')) {
                    // Decisions Made section
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h4 class="mt-4 summary-section"><i class="fas fa-check-circle summary-section-icon"></i>${line}</h4>`;
                }
                else if(line.startsWith('Action Items:')) {
                    // Action Items section
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h4 class="mt-4 summary-section"><i class="fas fa-tasks summary-section-icon"></i>${line}</h4>`;
                }
                else if(line.startsWith('Important Dates/Deadlines:')) {
                    // Important Dates section
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<h4 class="mt-4 summary-section"><i class="fas fa-calendar-alt summary-section-icon"></i>${line}</h4>`;
                }
                else if(line.startsWith('- ') || line.startsWith('• ')) {
                    // List item
                    if(!inList) {
                        html += '<ul class="summary-list">';
                        inList = true;
                    }
                    let itemText = line.substring(2).trim();
                    html += `<li>${itemText}</li>`;
                }
                else {
                    // Regular paragraph
                    if(inList) {
                        html += '</ul>';
                        inList = false;
                    }
                    html += `<p>${line}</p>`;
                }
            }
            
            // Close list if still open
            if(inList) {
                html += '</ul>';
            }
            
            return `<div class="summary-container">${html}</div>`;
        }
        
        // Copy transcript
        $('#copy-transcript-btn').click(function() {
            const transcript = document.getElementById('transcript-text').innerText;
            navigator.clipboard.writeText(transcript).then(function() {
                alert('Transcript copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        });
        
        // Download transcript
        $('#download-transcript-btn').click(function() {
            const transcript = document.getElementById('transcript-text').innerText;
            const blob = new Blob([transcript], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '{{ meeting[1] }}_transcript.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        
        // Search transcript
        $('#search-button').click(function() {
            searchTranscript();
        });
        
        $('#search-input').keypress(function(e) {
            if (e.which === 13) {
                searchTranscript();
            }
        });
        
        function searchTranscript() {
            const searchText = $('#search-input').val().trim();
            if (!searchText) return;
            
            const transcriptElement = document.getElementById('transcript-text');
            const text = transcriptElement.innerText;
            const regex = new RegExp(searchText, 'gi');
            
            // Clear previous highlights
            transcriptElement.innerHTML = text;
            
            if (text.match(regex)) {
                transcriptElement.innerHTML = text.replace(regex, match => `<span class="highlight">${match}</span>`);
                
                // Scroll to first match
                const firstMatch = document.querySelector('.highlight');
                if (firstMatch) {
                    firstMatch.scrollIntoView({behavior: 'smooth', block: 'center'});
                }
            } else {
                alert('No matches found');
            }
        }
        
        // Share meeting modal
        $('#share-meeting-btn').click(function() {
            const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
            shareModal.show();
        });
        
        $('#copy-link-btn').click(function() {
            const linkInput = document.getElementById('share-link');
            linkInput.select();
            navigator.clipboard.writeText(linkInput.value).then(function() {
                $('#copy-link-btn').html('<i class="fas fa-check"></i>');
                setTimeout(function() {
                    $('#copy-link-btn').html('<i class="fas fa-copy"></i>');
                }, 2000);
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        });
    });
</script>
{% endblock %}