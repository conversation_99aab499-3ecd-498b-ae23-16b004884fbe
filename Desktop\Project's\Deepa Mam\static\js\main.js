/**
 * Main JavaScript file for Meeting Recorder application
 */

// Show current year in footer
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('footer')) {
        const year = new Date().getFullYear();
        document.querySelector('footer .text-muted').innerHTML = 
            document.querySelector('footer .text-muted').innerHTML.replace('{{ now.year }}', year);
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Automatically close alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Confirm deletions
    document.querySelectorAll('.confirm-delete').forEach(function(element) {
        element.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this item? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
});

// Handle audio recording UI feedback
function updateRecordingStatus(isRecording) {
    const recordButton = document.getElementById('record-button');
    const stopButton = document.getElementById('stop-button');
    const recordingIndicator = document.getElementById('recording-indicator');
    
    if (!recordButton || !stopButton) return;
    
    if (isRecording) {
        recordButton.disabled = true;
        stopButton.disabled = false;
        if (recordingIndicator) {
            recordingIndicator.style.display = 'inline-block';
        }
    } else {
        recordButton.disabled = false;
        stopButton.disabled = true;
        if (recordingIndicator) {
            recordingIndicator.style.display = 'none';
        }
    }
}

// Format timestamps
function formatTimestamp(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// Format duration in minutes and seconds
function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
    } else {
        return `${remainingSeconds}s`;
    }
}
