# Python bytecode files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# VideoListiner specific files
app.log
*.wav
*.mp3
*.mp4
*.ogg
/static/recordings/
/ffmpeg/
/models/
/whisper_models/
*.db
*.sqlite
*.sqlite3
*.csv

# Virtual Environment
venv/
env/
ENV/
.env/
.venv/
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml
*.cover

# Documentation
docs/_build/
site/

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Logs
logs/
*.log
npm-debug.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local configuration
*.local.py
config.local.py
secrets.py
.env
