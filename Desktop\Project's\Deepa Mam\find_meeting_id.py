import os
import sqlite3
import sys
from datetime import datetime

def find_meeting_id_by_title_date(db_path, title, date_str):
    """Find meeting ID by title and date"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Parse date string to datetime
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        print(f"Error: Invalid date format. Expected format: YYYY-MM-DD HH:MM:SS")
        return None
    
    # Get date parts for flexible matching (within the same day)
    date_only = date_obj.strftime('%Y-%m-%d')
    
    # Search for meetings with matching title and date
    cursor.execute('''
    SELECT id, title, start_time, end_time
    FROM meetings
    WHERE title = ? AND start_time LIKE ?
    ''', (title, f"{date_only}%"))
    
    results = cursor.fetchall()
    conn.close()
    
    return results

def update_transcript_with_id(transcript_path, meeting_id):
    """Update transcript file to include meeting ID"""
    with open(transcript_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Check if Meeting ID is already present
    for i, line in enumerate(lines):
        if line.startswith("Meeting ID:"):
            lines[i] = f"Meeting ID: {meeting_id}\n"
            break
    else:
        # Add Meeting ID after Duration line
        for i, line in enumerate(lines):
            if line.startswith("Duration:"):
                lines.insert(i+1, f"Meeting ID: {meeting_id}\n")
                break
    
    with open(transcript_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"Updated transcript file with Meeting ID: {meeting_id}")

def process_transcript_file(transcript_path, db_path='meetings.db'):
    """Process transcript file to find and add meeting ID"""
    try:
        with open(transcript_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Extract title and date
        title = None
        date = None
        
        for line in lines:
            if line.startswith("Meeting:"):
                title = line.replace("Meeting:", "").strip()
            elif line.startswith("Date:"):
                date = line.replace("Date:", "").strip()
            # If we already have a Meeting ID in the file, extract it
            elif line.startswith("Meeting ID:"):
                meeting_id = line.replace("Meeting ID:", "").strip()
                print(f"This transcript already has Meeting ID: {meeting_id}")
                return meeting_id
        
        if not title or not date:
            print("Error: Could not find Meeting title or Date in transcript file")
            return None
        
        # Search for meeting in database
        results = find_meeting_id_by_title_date(db_path, title, date)
        
        if not results:
            print(f"No matching meeting found for title '{title}' and date '{date}'")
            return None
        
        if len(results) == 1:
            meeting_id = results[0][0]
            print(f"Found meeting ID: {meeting_id}")
            update_transcript_with_id(transcript_path, meeting_id)
            return meeting_id
        else:
            print(f"Found multiple matching meetings:")
            for i, (mid, mtitle, start, end) in enumerate(results):
                print(f"{i+1}. ID: {mid} - {mtitle} ({start} to {end})")
            
            choice = input("\nEnter number of correct meeting (or 'q' to quit): ")
            if choice.lower() == 'q':
                return None
            
            try:
                selected = int(choice) - 1
                if 0 <= selected < len(results):
                    meeting_id = results[selected][0]
                    update_transcript_with_id(transcript_path, meeting_id)
                    return meeting_id
                else:
                    print("Invalid selection")
                    return None
            except ValueError:
                print("Invalid input")
                return None
    
    except Exception as e:
        print(f"Error processing transcript file: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python find_meeting_id.py <transcript_file_path>")
        print("Example: python find_meeting_id.py recordings/transcripts/transcript_20250409_154815.txt")
        
        # If no argument provided, look for any transcript files in default location
        transcript_dir = os.path.join("recordings", "transcripts")
        if os.path.exists(transcript_dir):
            transcripts = [f for f in os.listdir(transcript_dir) if f.startswith("transcript_") and f.endswith(".txt")]
            
            if transcripts:
                print("\nAvailable transcript files:")
                for i, t in enumerate(transcripts):
                    print(f"{i+1}. {t}")
                
                choice = input("\nEnter number of transcript to process (or 'q' to quit): ")
                if choice.lower() != 'q':
                    try:
                        selected = int(choice) - 1
                        if 0 <= selected < len(transcripts):
                            transcript_path = os.path.join(transcript_dir, transcripts[selected])
                            process_transcript_file(transcript_path)
                    except ValueError:
                        print("Invalid input")
            else:
                print("\nNo transcript files found in recordings/transcripts directory")
    else:
        transcript_path = sys.argv[1]
        if os.path.exists(transcript_path):
            process_transcript_file(transcript_path)
        else:
            print(f"Error: File not found: {transcript_path}")
