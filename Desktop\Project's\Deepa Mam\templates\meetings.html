{% extends "base.html" %}

{% block title %}All Meetings{% endblock %}

{% block extra_css %}
<style>
    :root {
        --dark-bg: #121212;
        --dark-card: #1E1E1E;
        --dark-card-header: #161616;
        --dark-primary: #FF8C00;
        --dark-border: #333333;
        --dark-text: #000000;
        --dark-text-secondary: #6e360c;
        --dark-input-bg: #2D2D2D;
        --dark-highlight: rgba(255, 140, 0, 0.3);
        --dark-table-hover: rgba(255, 140, 0, 0.1);
        --dark-table-border: #333333;
        --dark-button-hover: #333333;
    }
    
    body {
        background-color: var(--dark-bg);
        color: var(--dark-text);
    }
    
    .card {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }
    
    .card-header {
        background-color: var(--dark-card-header);
        border-bottom-color: var(--dark-border);
        padding: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .card-body {
        padding: 1.5rem;
        background-color: var(--dark-card);
    }
    
    .btn-primary {
        background-color: var(--dark-primary);
        border-color: var(--dark-primary);
        color: #000000;
        font-weight: 500;
        border-radius: 6px;
        padding: 0.5rem 1.25rem;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background-color: #E67E00;
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(255, 140, 0, 0.3);
        color: #000000;
    }
    
    .btn-outline-primary {
        color: var(--dark-primary);
        border-color: var(--dark-primary);
        background-color: transparent;
    }
    
    .btn-outline-primary i {
        color: var(--dark-primary);
    }
    
    .btn-outline-success {
        color: #4ddb6e;
        border-color: #28a745;
        background-color: transparent;
    }
    
    .btn-outline-success i {
        color: #4ddb6e;
    }
    
    /* Enhanced Table Styling */
    .table {
        color: var(--dark-text);
        border-color: var(--dark-table-border);
        margin-bottom: 0;
        background-color: #121212;
    }
    
    .table thead th {
        border-bottom-color: var(--dark-table-border);
        color: var(--dark-text);
        background-color: #141414;
        padding: 1rem;
        font-weight: 600;
        white-space: nowrap;
        cursor: pointer;
        transition: background-color 0.2s ease;
        position: relative;
    }
    
    .table thead th:hover {
        background-color: #191919;
    }
    
    .table thead th.sort-asc::after {
        content: "▲";
        position: absolute;
        right: 8px;
        color: var(--dark-primary);
    }
    
    .table thead th.sort-desc::after {
        content: "▼";
        position: absolute;
        right: 8px;
        color: var(--dark-primary);
    }
    
    .table td, .table th {
        border-color: var(--dark-table-border);
        color: var(--dark-text);
        padding: 1rem;
        vertical-align: middle;
    }
    
    .table-hover tbody tr {
        background-color: #121212;
        transition: background-color 0.2s ease;
    }
    
    .table-hover tbody tr:nth-of-type(odd) {
        background-color: #151515;
    }
    
    /* Add a subtle hover effect with better visibility */
    .table-hover tbody tr:hover {
        background-color: rgba(255, 140, 0, 0.1);
        cursor: pointer;
    }
    
    /* Style all table text to ensure visibility */
    .table-responsive {
        background-color: #121212;
    }
    
    table, th, td, tr {
        color: var(--dark-text) !important;
        background-color: inherit;
    }
    
    /* Fix any Bootstrap overrides */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: #151515;
    }
    
    .table .thead-dark th {
        background-color: #141414;
        color: var(--dark-text);
    }

    /* Ensure action button text is visible */
    .btn-group .btn {
        color: inherit;
    }
    
    /* Make sure any highlighted elements have proper contrast */
    .highlight, .table .highlight {
        background-color: rgba(255, 140, 0, 0.2);
        color: var(--dark-text);
    }
    
    /* Fix any table caption or footer */
    .table caption {
        color: var(--dark-text-secondary);
    }
    
    /* Make pagination controls visible */
    .page-link {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
        color: var(--dark-text);
    }
    
    .page-item.active .page-link {
        background-color: var(--dark-primary);
        border-color: var(--dark-primary);
        color: #000;
    }
    
    .page-item.disabled .page-link {
        background-color: var(--dark-card);
        border-color: var(--dark-border);
        color: var(--dark-text-secondary);
    }
    
    /* Enhanced Action Buttons */
    .btn-group .btn {
        border-radius: 4px !important;
        margin: 0 2px;
        padding: 0.375rem 0.75rem;
        transition: all 0.2s ease;
    }
    
    .btn-group .btn:hover {
        transform: translateY(-2px);
    }
    
    .btn-outline-primary:hover {
        background-color: rgba(255, 140, 0, 0.15) !important;
        color: var(--dark-primary) !important;
        box-shadow: 0 2px 6px rgba(255, 140, 0, 0.2);
    }
    
    .btn-outline-success:hover {
        background-color: rgba(77, 219, 110, 0.15) !important;
        color: #4ddb6e !important;
        box-shadow: 0 2px 6px rgba(77, 219, 110, 0.2);
    }
    
    /* Enhanced Empty State */
    .alert-info {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--dark-text);
        border: none;
        border-left: 4px solid rgba(23, 162, 184, 0.5);
        border-radius: 4px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
    }
    
    .alert-info i {
        font-size: 2.5rem;
        margin-right: 1.5rem;
        color: rgba(23, 162, 184, 0.7);
    }
    
    .alert-info .alert-heading {
        color: var(--dark-text);
    }
    
    /* Card View Option */
    .view-toggle {
        margin-right: 1rem;
    }
    
    .view-toggle .btn {
        border-color: var(--dark-border);
        color: var(--dark-text);
        background-color: var(--dark-card-header);
        padding: 0.25rem 0.75rem;
    }
    
    .view-toggle .btn.active {
        background-color: var(--dark-primary);
        color: #000;
        border-color: var(--dark-primary);
    }
    
    .view-toggle .btn:hover:not(.active) {
        background-color: var(--dark-button-hover);
        color: var(--dark-text);
    }
    
    .meeting-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .meeting-card {
        background-color: var(--dark-card);
        border: 1px solid var(--dark-border);
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        color: var(--dark-text);
    }
    
    .meeting-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
    }
    
    .meeting-card-header {
        background-color: var(--dark-card-header);
        padding: 1rem;
        border-bottom: 1px solid var(--dark-border);
    }
    
    .meeting-card-title {
        margin: 0;
        font-size: 1.1rem;
        color: var(--dark-text);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .meeting-card-body {
        background-color: var(--dark-card);
        padding: 1rem;
    }
    
    .meeting-card-info {
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        color: var(--dark-text-secondary);
        font-size: 0.9rem;
    }
    
    .meeting-card-info i {
        width: 20px;
        margin-right: 8px;
        color: var(--dark-primary);
    }
    
    .meeting-card-actions {
        border-top: 1px solid var(--dark-border);
        padding: 1rem;
        display: flex;
        justify-content: space-between;
    }
    
    /* Search Bar */
    .search-container {
        max-width: 400px;
        margin-bottom: 1.5rem;
    }
    
    .search-container .input-group {
        background-color: var(--dark-card);
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid var(--dark-border);
    }
    
    .search-container .form-control {
        background-color: var(--dark-input-bg);
        border: none;
        color: var(--dark-text);
        padding: 0.625rem 1rem;
        box-shadow: none;
    }
    
    .search-container .form-control:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.2);
        background-color: var(--dark-input-bg);
        color: var(--dark-text);
    }
    
    .search-container .form-control::placeholder {
        color: var(--dark-text-secondary);
    }
    
    .search-container .btn {
        background-color: var(--dark-primary);
        color: #000;
        border: none;
    }
    
    /* Ensure all font awesome icons are visible */
    .fas {
        color: inherit;
    }
    
    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-fade-in {
        animation: fadeIn 0.3s ease forwards;
    }
    
    /* Responsive styling */
    @media (max-width: 767px) {
        .card-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .card-header-actions {
            margin-top: 1rem;
            width: 100%;
            display: flex;
            justify-content: space-between;
        }
        
        .card-header .btn {
            margin-top: 10px;
            width: 100%;
        }
        
        .btn-group {
            display: flex;
            width: 100%;
        }
        
        .btn-group .btn {
            flex: 1;
        }
        
        .meeting-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="card shadow-sm">
    <div class="card-header">
        <h2 class="mb-0">All Meetings</h2>
        <div class="d-flex align-items-center">
            <div class="view-toggle btn-group me-3" role="group" aria-label="View toggle">
                <button type="button" class="btn active" id="table-view-btn">
                    <i class="fas fa-table me-1"></i> Table
                </button>
                <button type="button" class="btn" id="card-view-btn">
                    <i class="fas fa-th-large me-1"></i> Cards
                </button>
            </div>
            <a href="{{ url_for('record_meeting') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> New Meeting
            </a>
        </div>
    </div>
    
    <div class="card-body">
        <!-- Search Box -->
        <div class="search-container">
            <div class="input-group mb-3">
                <input type="text" id="meeting-search" class="form-control" placeholder="Search meetings..." aria-label="Search meetings">
                <button class="btn btn-primary" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        
        {% if meetings %}
        <!-- Table View -->
        <div id="table-view" class="table-responsive">
            <table class="table table-hover" id="meetings-table">
                <thead>
                    <tr>
                        <th data-sort="id">ID</th>
                        <th data-sort="title">Title</th>
                        <th data-sort="date">Date</th>
                        <th data-sort="duration">Duration</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for meeting in meetings %}
                    <tr data-meeting-id="{{ meeting[0] }}">
                        <td>{{ meeting[0] }}</td>
                        <td>{{ meeting[1] }}</td>
                        <td>{{ meeting[2] }}</td>
                        <td>
                            {% if meeting[2] and meeting[3] %}
                                {% if meeting[2] is string and meeting[3] is string %}
                                    --
                                {% else %}
                                    {% set duration_minutes = ((meeting[3] - meeting[2]).total_seconds() / 60)|int %}
                                    {{ duration_minutes }} mins
                                {% endif %}
                            {% else %}
                                -- mins
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('meeting_detail', meeting_id=meeting[0]) }}"
                                    class="btn btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('chat', meeting_id=meeting[0]) }}"
                                    class="btn btn-outline-success" title="Chat">
                                    <i class="fas fa-comments"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Card View (Hidden by Default) -->
        <div id="card-view" class="meeting-grid" style="display: none;">
            {% for meeting in meetings %}
            <div class="meeting-card animate-fade-in" data-meeting-id="{{ meeting[0] }}">
                <div class="meeting-card-header">
                    <h3 class="meeting-card-title">{{ meeting[1] }}</h3>
                </div>
                <div class="meeting-card-body">
                    <div class="meeting-card-info">
                        <i class="fas fa-calendar-alt"></i>
                        <span>{{ meeting[2] }}</span>
                    </div>
                    <div class="meeting-card-info">
                        <i class="fas fa-clock"></i>
                        <span>
                        {% if meeting[2] and meeting[3] %}
                            {% if meeting[2] is string and meeting[3] is string %}
                                Duration unknown
                            {% else %}
                                {% set duration_minutes = ((meeting[3] - meeting[2]).total_seconds() / 60)|int %}
                                {{ duration_minutes }} minutes
                            {% endif %}
                        {% else %}
                            Duration unknown
                        {% endif %}
                        </span>
                    </div>
                    <div class="meeting-card-info">
                        <i class="fas fa-tag"></i>
                        <span>Meeting #{{ meeting[0] }}</span>
                    </div>
                </div>
                <div class="meeting-card-actions">
                    <a href="{{ url_for('meeting_detail', meeting_id=meeting[0]) }}" 
                       class="btn btn-sm btn-outline-primary" title="View Details">
                        <i class="fas fa-eye me-1"></i> View
                    </a>
                    <a href="{{ url_for('chat', meeting_id=meeting[0]) }}" 
                       class="btn btn-sm btn-outline-success" title="Chat">
                        <i class="fas fa-comments me-1"></i> Chat
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <div>
                <h4 class="alert-heading">No Meetings Yet</h4>
                <p class="mb-0">No meetings recorded yet. Click "New Meeting" to create your first meeting!</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Handle click on table rows
        $('#meetings-table tbody tr').click(function(e) {
            // Only navigate if the click wasn't on an action button
            if (!$(e.target).closest('a').length && !$(e.target).closest('button').length) {
                const meetingId = $(this).data('meeting-id');
                window.location.href = `/meeting/${meetingId}`;
            }
        });

        // View toggle functionality
        $('#table-view-btn').click(function() {
            $(this).addClass('active');
            $('#card-view-btn').removeClass('active');
            $('#table-view').show();
            $('#card-view').hide();
        });
        
        $('#card-view-btn').click(function() {
            $(this).addClass('active');
            $('#table-view-btn').removeClass('active');
            $('#card-view').show();
            $('#table-view').hide();
            
            // Trigger animation when switching to card view
            $('.meeting-card').each(function(index) {
                const card = $(this);
                setTimeout(function() {
                    card.addClass('animate-fade-in');
                }, index * 50);
            });
        });
        
        // Meeting search functionality
        $('#meeting-search').on('keyup', function() {
            const searchText = $(this).val().toLowerCase();
            
            // Search in table view
            $('#meetings-table tbody tr').each(function() {
                const rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(searchText) > -1);
            });
            
            // Search in card view
            $('.meeting-card').each(function() {
                const cardText = $(this).text().toLowerCase();
                $(this).toggle(cardText.indexOf(searchText) > -1);
            });
        });
        
        // Table sorting
        let currentSort = { column: 'id', direction: 'asc' };
        
        $('th[data-sort]').click(function() {
            const column = $(this).data('sort');
            
            // Reset all headers
            $('th').removeClass('sort-asc sort-desc');
            
            // Update sort direction
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort = { column: column, direction: 'asc' };
            }
            
            // Add sort indicator
            $(this).addClass(`sort-${currentSort.direction}`);
            
            // Do the sorting
            const rows = $('#meetings-table tbody tr').toArray();
            rows.sort(function(a, b) {
                let valA, valB;
                
                if (column === 'id') {
                    valA = parseInt($(a).find('td:eq(0)').text(), 10);
                    valB = parseInt($(b).find('td:eq(0)').text(), 10);
                } else if (column === 'duration') {
                    valA = parseInt($(a).find('td:eq(3)').text(), 10) || 0;
                    valB = parseInt($(b).find('td:eq(3)').text(), 10) || 0;
                } else {
                    valA = $(a).find(`td:eq(${column === 'title' ? 1 : 2})`).text();
                    valB = $(b).find(`td:eq(${column === 'title' ? 1 : 2})`).text();
                }
                
                if (currentSort.direction === 'asc') {
                    return valA > valB ? 1 : -1;
                } else {
                    return valA < valB ? 1 : -1;
                }
            });
            
            // Re-append rows in new order
            $('#meetings-table tbody').empty().append(rows);
        });
    });
</script>
{% endblock %}