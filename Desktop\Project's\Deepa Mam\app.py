import os
import sys
import queue
import threading
import sqlite3
import wave
import pyaudio
from datetime import datetime
import google.generativeai as genai
import whisper
from dotenv import load_dotenv
import subprocess
import json
import logging
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, abort
from werkzeug.utils import secure_filename
from threading import local

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='app.log',
    filemode='a'
)
logger = logging.getLogger('meeting_recorder')

load_dotenv()

# Create Flask app
app = Flask(__name__, 
    template_folder='templates',
    static_folder='static'
)
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', os.urandom(24).hex())
app.config['UPLOAD_FOLDER'] = 'recordings'
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max upload size
app.config['TEMPLATES_AUTO_RELOAD'] = True

# Thread-local storage
thread_local = local()

# Add datetime to all templates
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

# Add custom filters
@app.template_filter('datetime')
def parse_datetime(value):
    """Convert a string to datetime object"""
    if isinstance(value, str):
        try:
            return datetime.fromisoformat(value)
        except ValueError:
            try:
                # Try different formats
                return datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return datetime.now()  # Fallback
    return value

def download_ffmpeg():
    """Download and install FFmpeg automatically"""
    import tempfile
    import shutil
    import platform
    import requests
    import zipfile
    
    print("🔄 Attempting to download FFmpeg automatically...")
    logger.info("Attempting to download FFmpeg automatically")
    
    temp_dir = tempfile.mkdtemp()
    success = False
    
    try:
        system = platform.system().lower()
        
        if system == 'windows':
            # Download for Windows
            url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
            zip_path = os.path.join(temp_dir, "ffmpeg.zip")
            
            print(f"⬇️ Downloading FFmpeg from {url}")
            logger.info(f"Downloading FFmpeg from {url}")
            
            response = requests.get(url, stream=True)
            if response.status_code != 200:
                print(f"❌ Failed to download FFmpeg. Status code: {response.status_code}")
                return False
                
            total_size = int(response.headers.get('content-length', 0))
            block_size = 8192
            downloaded = 0
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=block_size):
                    f.write(chunk)
                    downloaded += len(chunk)
                    # Show progress
                    done = int(50 * downloaded / total_size) if total_size > 0 else 0
                    sys.stdout.write(f"\r[{'=' * done}{' ' * (50-done)}] {downloaded//(1024*1024)}MB/{total_size//(1024*1024)}MB")
                    sys.stdout.flush()
            
            print("\n📦 Extracting FFmpeg...")
            logger.info("Extracting FFmpeg")
            
            # Create final directory
            ffmpeg_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg')
            os.makedirs(ffmpeg_dir, exist_ok=True)
            
            # Extract ZIP file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Find bin directory in zip
                bin_files = [f for f in zip_ref.namelist() if f.endswith(('.exe', '.dll')) and '/bin/' in f]
                
                if bin_files:
                    # Extract binary files to ffmpeg directory
                    for file in bin_files:
                        # Extract file name from path
                        file_name = os.path.basename(file)
                        # Extract file to output directory
                        with zip_ref.open(file) as source, open(os.path.join(ffmpeg_dir, file_name), 'wb') as target:
                            shutil.copyfileobj(source, target)
                else:
                    print("❌ Could not find FFmpeg binaries in the downloaded archive")
                    return False
            
            # Add to PATH
            os.environ['PATH'] = ffmpeg_dir + os.pathsep + os.environ['PATH']
            
            print(f"✅ FFmpeg successfully installed to {ffmpeg_dir}")
            logger.info(f"FFmpeg successfully installed to {ffmpeg_dir}")
            success = True
            
        elif system == 'darwin':  # macOS
            print("ℹ️ On macOS, please install FFmpeg using Homebrew:")
            print("   brew install ffmpeg")
        elif system == 'linux':
            print("ℹ️ On Linux, please install FFmpeg using your package manager:")
            print("   sudo apt-get update && sudo apt-get install -y ffmpeg")
        else:
            print(f"❌ Unsupported platform: {system}")
            
        return success
        
    except Exception as e:
        print(f"❌ Error downloading FFmpeg: {str(e)}")
        logger.exception(f"Error downloading FFmpeg: {str(e)}")
        return False
    finally:
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

class MeetingRecorder:
    def __init__(self, config):
        self.config = config
        self.audio = pyaudio.PyAudio()
        self.frames = []
        self.mic_frames = []  # New array to store microphone frames
        self.system_frames = []  # New array to store system audio frames
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.mic_queue = queue.Queue()  # Queue for microphone audio
        self.system_queue = queue.Queue()  # Queue for system audio
        self.db_path = config['db_path']
        self.init_db_schema()
        
        # Set default value for ffmpeg_available before check_ffmpeg is called
        self.ffmpeg_available = False
        
        # Check for FFmpeg first
        self.check_ffmpeg()
        
        # Then set up AI components
        self.setup_ai()

    def check_ffmpeg(self):
        """Check for FFmpeg and offer to download it if missing"""
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
            self.ffmpeg_available = True
            print("✅ FFmpeg found and available.")
            logger.info("FFmpeg found and available")
        except (subprocess.SubprocessError, FileNotFoundError):
            print("\n⚠️ FFmpeg not found. Some features may be limited.")
            logger.warning("FFmpeg not found")
            self.ffmpeg_available = False
            
            # Check if we're in a directory we can write to
            if os.access(os.path.dirname(os.path.abspath(__file__)), os.W_OK):
                print("FFmpeg is required for audio transcription.")
                
                # For web interface, we should auto-download without prompting
                if 'FLASK_APP' in os.environ or 'WERKZEUG_RUN_MAIN' in os.environ:
                    print("Attempting automatic download of FFmpeg...")
                    self.ffmpeg_available = download_ffmpeg()
                else:
                    # For CLI, we can prompt the user
                    if input("Would you like to download FFmpeg automatically? (y/n): ").lower() == 'y':
                        self.ffmpeg_available = download_ffmpeg()
            
            if not self.ffmpeg_available:
                print("\nManual installation instructions:")
                print("1. Download FFmpeg from https://ffmpeg.org/download.html")
                print("2. Add it to your PATH or place it in the 'ffmpeg' folder in this directory")

    def setup_ai(self):
        """Initialize AI services based on configuration"""
        # Initialize Whisper for local transcription
        if self.config['use_local_whisper']:
            if not self.ffmpeg_available:
                print("FFmpeg not available. Disabling local Whisper transcription.")
                self.config['use_local_whisper'] = False
            else:
                try:
                    print(f"Loading Whisper model: {self.config['whisper_model_size']}...")
                    self.whisper_model = whisper.load_model(self.config['whisper_model_size'])
                    print("Whisper model loaded successfully.")
                except Exception as e:
                    print(f"Error loading Whisper model: {str(e)}")
                    print("Disabling local transcription.")
                    self.config['use_local_whisper'] = False

        # Initialize Gemini for AI features
        if self.config['use_gemini'] and self.config['gemini_key']:
            try:
                print("Initializing Google Gemini service...")
                genai.configure(api_key=self.config['gemini_key'])
                self.gemini_model = genai.GenerativeModel('gemini-2.0-flash')
                print("Gemini AI service initialized successfully.")
            except Exception as e:
                print(f"Error initializing Gemini: {str(e)}")
                print("Disabling Gemini AI features.")
                self.config['use_gemini'] = False
                
        # Warn if no transcription or AI capabilities are available
        if not self.config['use_local_whisper']:
            print("WARNING: Local transcription is not available. Recordings will not be transcribed.")
        if not self.config['use_gemini']:
            print("WARNING: Gemini AI is not available. Chat and summary features will not work.")

    def get_db_connection(self):
        """Get a thread-specific database connection"""
        if not hasattr(thread_local, 'db_connection'):
            thread_local.db_connection = sqlite3.connect(self.db_path)
            thread_local.db_connection.row_factory = sqlite3.Row
        return thread_local.db_connection

    def init_db_schema(self):
        """Initialize SQLite database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS meetings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            start_time DATETIME,
            end_time DATETIME,
            participants TEXT,
            audio_file_path TEXT,
            raw_transcript TEXT
        )''')
        
        cursor.execute('''
        CREATE VIRTUAL TABLE IF NOT EXISTS meeting_search 
        USING fts5(id, title, content, tokenize="porter unicode61")
        ''')
        
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            meeting_id INTEGER,
            timestamp DATETIME,
            user_query TEXT,
            ai_response TEXT,
            FOREIGN KEY (meeting_id) REFERENCES meetings(id)
        )''')
        
        conn.commit()
        conn.close()

    def start_recording(self, meeting_title=""):
        if self.is_recording:
            return
            
        self.frames = []
        self.mic_frames = []
        self.system_frames = []
        self.is_recording = True
        self.current_meeting = {
            'title': meeting_title,
            'start_time': datetime.now(),
            'participants': ""
        }
        
        # Get list of audio devices
        info = self.audio.get_host_api_info_by_index(0)
        num_devices = info.get('deviceCount')
        
        mic_device_index = self.config['input_device_index']  # Default mic
        system_device_index = None  # System audio device (like Stereo Mix)
        
        # Try to find system audio device like "Stereo Mix" or "VB-Audio"
        for i in range(num_devices):
            device_info = self.audio.get_device_info_by_index(i)
            device_name = device_info.get('name', '').lower()
            if (device_info.get('maxInputChannels') > 0 and 
                ('stereo mix' in device_name or 'vb-audio' in device_name or 'cable output' in device_name)):
                system_device_index = i
                print(f"Found system audio device: {device_info.get('name')}")
                break
        
        # Start microphone stream
        self.mic_stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=self.config['channels'],
            rate=self.config['sample_rate'],
            input=True,
            frames_per_buffer=self.config['chunk_size'],
            input_device_index=mic_device_index,
            stream_callback=self.mic_callback
        )
        
        # If system audio device was found, start stream for it
        if system_device_index is not None:
            try:
                self.system_stream = self.audio.open(
                    format=pyaudio.paInt16,
                    channels=self.config['channels'],
                    rate=self.config['sample_rate'],
                    input=True,
                    frames_per_buffer=self.config['chunk_size'],
                    input_device_index=system_device_index,
                    stream_callback=self.system_callback
                )
                print("System audio recording started")
            except Exception as e:
                print(f"Could not open system audio stream: {str(e)}")
                self.system_stream = None
        else:
            print("No system audio device found. Only recording from microphone.")
            self.system_stream = None
        
        # Start processing threads
        self.mic_processing_thread = threading.Thread(target=self.process_mic_audio)
        self.mic_processing_thread.start()
        
        if self.system_stream is not None:
            self.system_processing_thread = threading.Thread(target=self.process_system_audio)
            self.system_processing_thread.start()
    
    def mic_callback(self, in_data, frame_count, time_info, status):
        self.mic_queue.put(in_data)
        return (in_data, pyaudio.paContinue)
    
    def system_callback(self, in_data, frame_count, time_info, status):
        self.system_queue.put(in_data)
        return (in_data, pyaudio.paContinue)
    
    def process_mic_audio(self):
        while self.is_recording or not self.mic_queue.empty():
            try:
                self.mic_frames.append(self.mic_queue.get(timeout=1))
            except queue.Empty:
                continue
    
    def process_system_audio(self):
        while self.is_recording or not self.system_queue.empty():
            try:
                self.system_frames.append(self.system_queue.get(timeout=1))
            except queue.Empty:
                continue
    
    def stop_recording(self):
        if not self.is_recording:
            return
            
        self.is_recording = False
        
        # Stop and close microphone stream
        self.mic_stream.stop_stream()
        self.mic_stream.close()
        
        # Stop system stream if it exists
        if hasattr(self, 'system_stream') and self.system_stream is not None:
            self.system_stream.stop_stream()
            self.system_stream.close()
        
        self.current_meeting['end_time'] = datetime.now()
        timestamp = self.current_meeting['start_time'].strftime('%Y%m%d_%H%M%S')
        
        # Save mic audio
        mic_path = os.path.join(self.config['audio_dir'], f"mic_{timestamp}.wav")
        with wave.open(mic_path, 'wb') as wf:
            wf.setnchannels(self.config['channels'])
            wf.setsampwidth(self.audio.get_sample_size(pyaudio.paInt16))
            wf.setframerate(self.config['sample_rate'])
            wf.writeframes(b''.join(self.mic_frames))
        
        # If we have system audio, save it and mix with mic audio
        if hasattr(self, 'system_stream') and self.system_stream is not None and self.system_frames:
            # Save system audio
            system_path = os.path.join(self.config['audio_dir'], f"system_{timestamp}.wav")
            with wave.open(system_path, 'wb') as wf:
                wf.setnchannels(self.config['channels'])
                wf.setsampwidth(self.audio.get_sample_size(pyaudio.paInt16))
                wf.setframerate(self.config['sample_rate'])
                wf.writeframes(b''.join(self.system_frames))
            
            # Mix the two audio files using FFmpeg if available
            mixed_path = os.path.join(self.config['audio_dir'], f"meeting_{timestamp}.wav")
            if self.ffmpeg_available:
                try:
                    # Use FFmpeg to mix both audio files
                    cmd = [
                        'ffmpeg', '-y',
                        '-i', mic_path,
                        '-i', system_path,
                        '-filter_complex', 'amix=inputs=2:duration=longest',
                        mixed_path
                    ]
                    subprocess.run(cmd, check=True, capture_output=True)
                    print(f"Mixed audio saved to {mixed_path}")
                    
                    # Use the mixed file for the meeting
                    audio_path = mixed_path
                except Exception as e:
                    print(f"Error mixing audio: {str(e)}")
                    audio_path = mic_path  # Fall back to mic audio if mixing fails
            else:
                print("FFmpeg not available, using microphone audio only")
                audio_path = mic_path
        else:
            # No system audio, use mic audio only
            mixed_path = os.path.join(self.config['audio_dir'], f"meeting_{timestamp}.wav")
            os.rename(mic_path, mixed_path)
            audio_path = mixed_path
        
        self.current_meeting['audio_file_path'] = audio_path
        self.current_meeting['raw_transcript'] = self.transcribe_audio(audio_path)
        self.save_transcript_to_file(self.current_meeting['raw_transcript'], timestamp)
        return self.save_meeting_to_db()
    
    def transcribe_audio(self, audio_path):
        if not os.path.exists(audio_path) or os.path.getsize(audio_path) == 0:
            return "[Audio file not found or empty]"
            
        if self.config['use_local_whisper'] and self.ffmpeg_available:
            try:
                return self.whisper_model.transcribe(audio_path)['text']
            except Exception:
                pass
        
        return "[Transcription failed]"
    
    def save_transcript_to_file(self, transcript, timestamp):
        os.makedirs(os.path.join(self.config['audio_dir'], 'transcripts'), exist_ok=True)
        text_path = os.path.join(self.config['audio_dir'], 'transcripts', f"transcript_{timestamp}.txt")
        
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(f"Meeting: {self.current_meeting['title']}\n")
            f.write(f"Date: {self.current_meeting['start_time'].strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Duration: {(self.current_meeting['end_time'] - self.current_meeting['start_time']).seconds // 60} minutes\n")
            f.write("\n--- TRANSCRIPT ---\n\n")
            f.write(transcript)
        
        return text_path
    
    def save_meeting_to_db(self):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO meetings (title, start_time, end_time, participants, audio_file_path, raw_transcript)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            self.current_meeting['title'],
            self.current_meeting['start_time'],
            self.current_meeting['end_time'],
            self.current_meeting['participants'],
            self.current_meeting['audio_file_path'],
            self.current_meeting['raw_transcript']
        ))
        
        self.current_meeting_id = cursor.lastrowid
        
        cursor.execute('''
        INSERT INTO meeting_search (id, title, content)
        VALUES (?, ?, ?)
        ''', (self.current_meeting_id, self.current_meeting['title'], self.current_meeting['raw_transcript']))
        
        conn.commit()
        return self.current_meeting_id
    
    def get_all_meetings(self):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        SELECT id, title, start_time, end_time FROM meetings
        ORDER BY start_time DESC
        ''')
        return cursor.fetchall()
    
    def get_meeting_details(self, meeting_id):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        SELECT id, title, start_time, end_time, participants, raw_transcript 
        FROM meetings WHERE id = ?
        ''', (meeting_id,))
        return cursor.fetchone()
        
    def get_meeting_transcript(self, meeting_id):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        SELECT raw_transcript FROM meetings WHERE id = ?
        ''', (meeting_id,))
        
        result = cursor.fetchone()
        return result[0] if result else None
        
    def generate_summary(self, meeting_id):
        transcript = self.get_meeting_transcript(meeting_id)
        if not transcript or not self.config['use_gemini']:
            return "Unable to generate summary. Transcript not found or Gemini not available."
        
        prompt = f"""
        Meeting Transcript:
        {transcript[:8000]}  # Limit size to avoid token limits
        
        Generate a concise summary with:
        1. Key Discussion Points
        2. Decisions Made
        3. Action Items (with owners if mentioned)
        4. Important Dates/Deadlines
        
        Use bullet points and keep it brief.
        """
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Error generating summary: {str(e)}"

    def ask_question(self, meeting_id, question):
        transcript = self.get_meeting_transcript(meeting_id)
        if not transcript:
            return "Meeting not found"
        
        if not self.config['use_gemini'] or not self.config['gemini_key']:
            return "Chat functionality is not available because Gemini AI is not configured. Please set up your GEMINI_KEY in the .env file."
            
        try:
            partial_match = self._check_partial_mention(transcript, question)
            if partial_match:
                return self._generate_hybrid_response(transcript, question, partial_match)
            
            if any(term in question.lower() for term in ['explain', 'detail', 'elaborate', 'more']):
                return self._generate_detailed_explanation(transcript, question)
            else:
                return self._get_meeting_specific_answer(transcript, question)
        except Exception as e:
            logger.exception(f"Error processing question: {str(e)}")
            return "I'm sorry, I couldn't process your question. Please try again with different wording or check the application logs for more information."
    
    def _check_partial_mention(self, transcript, question):
        entities = self._extract_entities(question)
        
        entity_check_prompt = f"""
        Meeting Transcript:
        {transcript[:5000]}
        
        Question entities: {', '.join(entities)}
        
        Check if each entity is mentioned in the transcript without the details asked in: "{question}"
        Return ONLY a JSON object like {{"Entity": "status"}} where status is one of:
        - "not_mentioned"
        - "mentioned_without_details" 
        - "fully_addressed"
        """
        
        try:
            response = self.gemini_model.generate_content(entity_check_prompt)
            import json
            import re
            json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group(0))
                for entity, status in result.items():
                    if status == "mentioned_without_details":
                        return entity
        except Exception:
            pass
        
        return None
    
    def _extract_entities(self, question):
        entity_prompt = f"Extract ONLY key entities from: \"{question}\". Return as JSON array: [\"Entity1\", \"Entity2\"]"
        try:
            response = self.gemini_model.generate_content(entity_prompt)
            import json
            import re
            list_match = re.search(r'\[.*\]', response.text, re.DOTALL)
            if list_match:
                return json.loads(list_match.group(0))
            else:
                return [word for word in question.split() if any(c.isupper() for c in word)]
        except Exception:
            return [w for w in question.split() if len(w) > 3]
    
    def _generate_hybrid_response(self, transcript, question, entity):
        prompt = f"""
        Meeting Transcript: {transcript[:5000]}
        
        User Question: {question}
        Entity mentioned in meeting: {entity}
        
        Give a DIRECT answer with:
        1. First sentence: What was actually said about {entity} in the meeting (be specific)
        2. Second part: Additional factual information about {entity} related to the question
        
        Be extremely concise. No labels, headers, or explanations about your answer.
        Use at most 3-5 sentences total.
        """
        
        response = self.gemini_model.generate_content(prompt).text
        cleaned_response = self._clean_response(response)
        return cleaned_response
    
    def _clean_response(self, text):
        import re
        text = re.sub(r'(?i)(\*\*)?PART \d+:(\*\*)?\s*', '', text)
        text = re.sub(r'^\s*[-*•]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'\*\*|\*|__|\^|#', '', text)
        text = re.sub(r'"\s*\n\s*"', ' ', text)
        text = re.sub(r'\n{2,}', '\n', text)
        text = re.sub(r'\s{2,}', ' ', text)
        return text.strip()
    
    def _generate_detailed_explanation(self, transcript, question):
        prompt = f"""
        Meeting Transcript:
        {transcript[:5000]}
        
        User Question: {question}
        
        ONLY use information from the transcript. If information isn't there, say "Not mentioned in the meeting."
        Give a clear, organized answer with specific references to what was said.
        """
        response = self.gemini_model.generate_content(prompt).text
        return self._clean_response(response)
    
    def _get_meeting_specific_answer(self, transcript, question):
        prompt = f"""
        Meeting Transcript:
        {transcript}
        
        User Question: {question}
        
        Give a DIRECT and CONCISE answer (1-3 sentences) based ONLY on the transcript.
        If not in transcript, only say "Not mentioned in the meeting."
        No explanations or additional commentary.
        """
        response = self.gemini_model.generate_content(prompt).text
        return self._clean_response(response)
    
    def chat_with_meeting(self, meeting_id):
        meeting = self.get_meeting_details(meeting_id)
        if not meeting:
            print("Meeting not found!")
            return
        
        print(f"\n=== Chatting about meeting: {meeting[1]} ===")
        print("Commands: 'summary', 'transcript', 'exit'")
        print("Ask simple questions for brief answers. Add 'explain' or 'details' for in-depth responses.")
        print("When you ask about topics mentioned in the meeting without details, I'll provide both meeting context and general information.")
        
        while True:
            query = input("\nAsk about this meeting: ").strip()
            
            if query.lower() in ['exit', 'quit', 'q']:
                break
            if not query:
                continue
                
            if query.lower() == 'summary':
                print("\n" + self.generate_summary(meeting_id))
                continue
                
            if query.lower() == 'transcript':
                print("\n" + meeting[5])
                continue
            
            print("\n" + self.ask_question(meeting_id, query))
    
    def close(self):
        self.audio.terminate()
        if hasattr(thread_local, 'db_connection'):
            thread_local.db_connection.close()
            del thread_local.db_connection

    def get_meeting_by_id_json(self, meeting_id):
        meeting = self.get_meeting_details(meeting_id)
        if not meeting:
            return None
            
        return {
            'id': meeting[0],
            'title': meeting[1],
            'start_time': meeting[2],
            'end_time': meeting[3],
            'participants': meeting[4],
            'transcript': meeting[5]
        }
            
    def search_meetings(self, query):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        SELECT m.id, m.title, m.start_time, snippet(meeting_search, 2, '<mark>', '</mark>', '...', 20) as snippet
        FROM meetings m
        JOIN meeting_search ms ON m.id = ms.id
        WHERE meeting_search MATCH ?
        ORDER BY rank
        LIMIT 10
        ''', (query,))
        
        results = cursor.fetchall()
        return [
            {
                'id': row[0],
                'title': row[1],
                'date': row[2],
                'snippet': row[3]
            }
            for row in results
        ]

    def save_chat_history(self, meeting_id, question, answer):
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO chat_history (meeting_id, timestamp, user_query, ai_response)
        VALUES (?, ?, ?, ?)
        ''', (meeting_id, datetime.now(), question, answer))
        conn.commit()

def create_recorder():
    config = {
        'sample_rate': 44100,
        'chunk_size': 1024,
        'channels': 2,
        'input_device_index': None,
        'audio_dir': 'recordings',
        'db_path': 'meetings.db',
        'use_local_whisper': True,
        'whisper_model_size': 'base',
        'use_gemini': True,
        'gemini_key': os.getenv('GEMINI_KEY')
    }
    
    os.makedirs(config['audio_dir'], exist_ok=True)
    return MeetingRecorder(config)

recorder = create_recorder()

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('error.html', error_code=404, 
                          message="Page not found. The requested URL was not found on the server."), 404

@app.errorhandler(500)
def internal_server_error(e):
    # Log the error for debugging
    logger.error(f"500 error: {str(e)}")
    return render_template('error.html', error_code=500,
                          message="Internal Server Error. The server encountered an unexpected condition."), 500

@app.errorhandler(Exception)
def handle_exception(e):
    # Log the stacktrace
    logger.exception("Unhandled exception: %s", str(e))
    return render_template('error.html', error_code=500,
                          message=f"An unexpected error occurred: {str(e)}"), 500

@app.route('/')
def index():
    try:
        meetings = recorder.get_all_meetings()
        return render_template('index.html', meetings=meetings[:5])
    except Exception as e:
        logger.exception("Error in index route: %s", str(e))
        return render_template('error.html', error_code=500,
                              message=f"Could not load meetings: {str(e)}")

@app.route('/meetings')
def meetings_list():
    meetings = recorder.get_all_meetings()
    return render_template('meetings.html', meetings=meetings)

@app.route('/meeting/<int:meeting_id>')
def meeting_detail(meeting_id):
    meeting = recorder.get_meeting_details(meeting_id)
    if not meeting:
        flash('Meeting not found', 'error')
        return redirect(url_for('meetings_list'))
    
    return render_template('meeting_detail.html', meeting=meeting)

@app.route('/record', methods=['GET', 'POST'])
def record_meeting():
    if request.method == 'POST':
        title = request.form['title']
        
        if not recorder.is_recording:
            recorder.start_recording(title)
            session['recording'] = True
            flash(f'Started recording: {title}', 'success')
        
        return redirect(url_for('recording_control'))
    
    return render_template('record.html')

@app.route('/recording-control')
def recording_control():
    recording = session.get('recording', False)
    return render_template('recording_control.html', recording=recording)

@app.route('/stop-recording', methods=['POST'])
def stop_recording():
    if recorder.is_recording:
        meeting_id = recorder.stop_recording()
        session['recording'] = False
        session['last_meeting_id'] = meeting_id
        flash('Recording stopped and saved', 'success')
        return redirect(url_for('meeting_detail', meeting_id=meeting_id))
    
    flash('No active recording to stop', 'error')
    return redirect(url_for('index'))

@app.route('/chat/<int:meeting_id>', methods=['GET', 'POST'])
def chat(meeting_id):
    meeting = recorder.get_meeting_details(meeting_id)
    if not meeting:
        flash('Meeting not found', 'error')
        return redirect(url_for('meetings_list'))
    
    return render_template('chat.html', meeting=meeting)

@app.route('/api/ask/<int:meeting_id>', methods=['POST'])
def api_ask_question(meeting_id):
    try:
        data = request.json
        question = data.get('question', '')
        
        if not question:
            return jsonify({'error': 'No question provided'}), 400
        
        answer = recorder.ask_question(meeting_id, question)
        recorder.save_chat_history(meeting_id, question, answer)
        
        return jsonify({'answer': answer})
    except Exception as e:
        logger.exception(f"Error in api_ask_question: {str(e)}")
        return jsonify({'error': f"Server error: {str(e)}"}), 500

@app.route('/api/summary/<int:meeting_id>')
def generate_summary(meeting_id):
    """Generate a summary for a specific meeting"""
    try:
        meeting = recorder.get_meeting_details(meeting_id)
        if not meeting:
            logger.warning(f"Summary generation failed: Meeting ID {meeting_id} not found")
            return jsonify({"error": "Meeting not found"}), 404

        transcript = meeting[5]
        
        if not transcript or transcript == "[Transcription failed]" or transcript == "[Audio file not found or empty]":
            logger.warning(f"Summary generation failed: No valid transcript for meeting ID {meeting_id}")
            return jsonify({"summary": "No valid transcript available for this meeting."}), 200
            
        # Generate summary
        if recorder.config['use_gemini'] and recorder.config['gemini_key']:
            try:
                # Create a new instance to avoid potential issues with existing one
                genai.configure(api_key=recorder.config['gemini_key'])
                gemini_model = genai.GenerativeModel('gemini-2.0-flash')
                
                # Limit transcript length to avoid token limits
                max_tokens = 7000
                limited_transcript = transcript[:max_tokens] if len(transcript) > max_tokens else transcript
                
                prompt = f"""
                Please provide a concise summary of the following meeting transcript, organized into these sections:
                - Key Discussion Points: Main topics and ideas discussed
                - Decisions Made: Clear decisions reached during the meeting
                - Action Items: Tasks assigned or actions to be taken
                - Important Dates/Deadlines: Any timeframes or deadlines mentioned
                
                Transcript:
                {limited_transcript}
                """
                
                # Set generation parameters for stability
                response = gemini_model.generate_content(
                    prompt,
                    generation_config={
                        "temperature": 0.2,
                        "top_p": 0.8,
                        "top_k": 40,
                        "max_output_tokens": 1024,
                    }
                )
                
                # Format the summary to ensure it renders properly as HTML
                summary = response.text
                
                # Clean up any markdown formatting that might cause display issues
                summary = summary.replace('**', '')  # Remove bold markers
                
                # Ensure proper line breaks and sections
                if not any(section in summary for section in ["Key Discussion Points", "Discussion Points", "Main Topics"]):
                    logger.warning("Generated summary missing expected sections, using fallback format")
                    summary = """
                    Key Discussion Points:
                    • No specific points could be identified in the transcript.
                    
                    Decisions Made:
                    • No clear decisions identified in the transcript.
                    
                    Action Items:
                    • No action items identified in the transcript.
                    
                    Important Dates/Deadlines:
                    • No dates or deadlines mentioned in the transcript.
                    """
                
                logger.info(f"Successfully generated summary for meeting ID {meeting_id}")
                return jsonify({"summary": summary})
                
            except Exception as e:
                logger.exception(f"Error generating summary with Gemini: {str(e)}")
                # Return a graceful error message but with 200 status code
                # This prevents the frontend from showing an error modal
                fallback_summary = """
                Unable to generate a summary at this time.
                
                Key Discussion Points:
                • Please try again in a few moments or check your API key configuration.
                
                Decisions Made:
                • Error processing transcript - details logged in server log.
                
                Action Items:
                • Check application logs for more information.
                """
                return jsonify({"summary": fallback_summary}), 200
        else:
            logger.warning("Summary generation failed: Gemini API is not configured")
            return jsonify({"summary": "Summary generation is not available because Gemini AI is not configured. Please set up your GEMINI_KEY in the .env file."}), 200
    except Exception as e:
        logger.exception(f"Unexpected error in summary generation: {str(e)}")
        return jsonify({"summary": "An unexpected error occurred while generating the summary. Please check the server logs for more information."}), 200

@app.route('/search')
def search():
    query = request.args.get('query', '')
    results = []
    
    if query:
        results = recorder.search_meetings(query)
    
    return render_template('search.html', query=query, results=results)

@app.route('/api/upload-audio', methods=['POST'])
def upload_audio():
    if 'audio' not in request.files:
        return jsonify({'error': 'No audio file provided'}), 400
    
    file = request.files['audio']
    title = request.form.get('title', 'Uploaded Meeting')
    
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    filename = secure_filename(file.filename)
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)
    
    transcript = recorder.transcribe_audio(filepath)
    
    conn = recorder.get_db_connection()
    cursor = conn.cursor()
    cursor.execute('''
    INSERT INTO meetings (title, start_time, end_time, participants, audio_file_path, raw_transcript)
    VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        title,
        datetime.now(),
        datetime.now(),
        "",
        filepath,
        transcript
    ))
    
    meeting_id = cursor.lastrowid
    
    cursor.execute('''
    INSERT INTO meeting_search (id, title, content)
    VALUES (?, ?, ?)
    ''', (meeting_id, title, transcript))
    
    conn.commit()
    
    return jsonify({
        'success': True,
        'meeting_id': meeting_id,
        'redirect': url_for('meeting_detail', meeting_id=meeting_id)
    })

@app.route('/about')
def about():
    """Renders the about page"""
    try:
        return render_template('about.html')
    except Exception as e:
        logger.exception(f"Error in about route: {str(e)}")
        return render_template('error.html', error_code=500, 
                              message=f"Could not load about page: {str(e)}")

def main(run_web=True):
    # Check for existing FFmpeg in app directory
    ffmpeg_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ffmpeg')
    if os.path.exists(ffmpeg_dir):
        os.environ['PATH'] = ffmpeg_dir + os.pathsep + os.environ['PATH']
        print(f"📁 Using FFmpeg from local directory: {ffmpeg_dir}")
        logger.info(f"Using FFmpeg from local directory: {ffmpeg_dir}")
    
    if run_web:
        # Fix for potential bugs in development mode
        os.environ['FLASK_ENV'] = 'production'
        
        try:
            # Create necessary directories
            os.makedirs('static', exist_ok=True)
            os.makedirs('templates', exist_ok=True)
            os.makedirs('recordings', exist_ok=True)
            
            # Make sure we have all required templates
            check_templates_exist()
            
            # Start the server
            logger.info("Starting Flask server")
            app.run(debug=False, threaded=True, host='0.0.0.0', port=5000)
        except Exception as e:
            logger.critical(f"Failed to start server: {str(e)}")
            print(f"\n❌ ERROR: Failed to start server: {str(e)}")
            print("Check app.log for details")
    else:
        recorder = create_recorder()
        try:
            while True:
                print("\n1. Record meeting\n2. List meetings\n3. Chat\n4. Search\n5. Exit")
                choice = input("Choose: ")
                
                if choice == '1':
                    recorder.start_recording(input("Meeting title: "))
                    input("Recording... Press Enter to stop")
                    meeting_id = recorder.stop_recording()
                    if input("Chat now? (y/n): ").lower() == 'y':
                        recorder.chat_with_meeting(meeting_id)
                
                elif choice == '2':
                    for m in recorder.get_all_meetings():
                        print(f"ID: {m[0]} - {m[1]} ({m[2]})")
                
                elif choice == '3':
                    recorder.chat_with_meeting(int(input("Meeting ID: ")))
                
                elif choice == '5':
                    break
                    
        finally:
            recorder.close()

def check_templates_exist():
    """Check if all required templates exist and create them if not"""
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    required_templates = ['index.html', 'error.html', 'chat.html', 'meeting_detail.html',
                         'meetings.html', 'record.html', 'recording_control.html', 'search.html']
    
    for template in required_templates:
        template_path = os.path.join(templates_dir, template)
        if not os.path.exists(template_path):
            logger.warning(f"Template '{template}' not found, creating placeholder")
            create_placeholder_template(template_path, template)

def create_placeholder_template(path, name):
    """Create a basic placeholder template"""
    content = f"""
    <!DOCTYPE html>
    <html>
        <head>
            <title>Meeting Recorder - {name}</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="alert alert-warning">
                    <h2>Template Warning</h2>
                    <p>The template '{name}' is a placeholder. Please create a proper template file.</p>
                </div>
                <a href="/" class="btn btn-primary">Return to Home</a>
            </div>
        </body>
    </html>
    """
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w') as f:
        f.write(content)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--cli':
        main(run_web=False)
    else:
        main(run_web=True)