<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Meeting Recorder{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #FF8C00;  /* Dark Orange */
            --primary-dark: #E67E00;
            --bg-dark: #121212;
            --card-dark: #1E1E1E;
            --text-primary: #E0E0E0;
            --text-secondary: #B0B0B0;
            --border-color: #333333;
        }
        
        body {
            background-color: var(--bg-dark);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .navbar {
            background-color: var(--bg-dark) !important;
            border-bottom: 1px solid var(--border-color);
        }
        
        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 600;
        }
        
        .nav-link {
            color: var(--text-primary) !important;
        }
        
        .nav-link.active {
            color: var(--primary-color) !important;
            font-weight: 500;
        }
        
        .form-control {
            background-color: #2E2E2E;
            border-color: var(--border-color);
            color: var(--text-primary);
        }
        
        .form-control:focus {
            background-color: #2E2E2E;
            border-color: var(--primary-color);
            color: var(--text-primary);
            box-shadow: 0 0 0 0.2rem rgba(255, 140, 0, 0.25);
        }
        
        .btn-light {
            background-color: #2E2E2E;
            border-color: var(--border-color);
            color: var(--text-primary);
        }
        
        .footer {
            background-color: var(--bg-dark) !important;
            border-top: 1px solid var(--border-color);
        }
        
        .text-muted {
            color: var(--text-secondary) !important;
        }
        
        .alert {
            border: 1px solid var(--border-color);
        }
        
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        .alert-success {
            background-color: rgba(25, 135, 84, 0.2);
            color: #198754;
        }
        
        .alert-info {
            background-color: rgba(13, 202, 240, 0.2);
            color: #0dcaf0;
        }
        
        .alert-warning {
            background-color: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        
        /* Remove all hover effects */
        a, button, .nav-link, .btn {
            transition: none !important;
        }
        
        a:hover, button:hover, .nav-link:hover, .btn:hover {
            transform: none !important;
            box-shadow: none !important;
            background-color: inherit !important;
            color: inherit !important;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .navbar-collapse {
                padding-top: 1rem;
            }
            
            .d-flex {
                margin-top: 1rem;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-microphone-alt me-2"></i> AI Recorder Agent
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('meetings_list') %}active{% endif %}" href="{{ url_for('meetings_list') }}">Meetings</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('record_meeting') %}active{% endif %}" href="{{ url_for('record_meeting') }}">Record</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('about') %}active{% endif %}" href="{{ url_for('about') }}">About</a>
                    </li>
                </ul>
                <form class="d-flex" action="{{ url_for('search') }}" method="get">
                    <input class="form-control me-2" type="search" placeholder="Search meetings..." name="query">
                    <button class="btn btn-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container my-4 flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <span class="text-muted">Meeting Recorder &copy; {{ now.year }}</span>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery (for AJAX) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>